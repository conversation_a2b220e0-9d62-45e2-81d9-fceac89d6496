 FAIL  server/src/platform/integration/testing-framework/__tests__/MemorySafetyIntegrationValidator.branch.test.ts (30.438 s, 53 MB heap size)
  MemorySafetyIntegrationValidator - Branch Coverage Tests
    Initialization Branch Coverage
      ✓ should cover both branches of validator initialization check (12 ms)
      ✓ should cover both branches of validation active check (5 ms)
      ✓ should cover both branches of monitoring enabled check (8 ms)
    Configuration Validation Branch Coverage
      ✓ should cover both branches of configuration validation (3 ms)
      ✓ should cover both branches of test type validation (3 ms)
      ✓ should cover both branches of test already enabled check (4 ms)
    Test Execution Branch Coverage
      ✓ should cover both branches of test suite validation (5 ms)
      ✕ should cover both branches of test execution error handling (7 ms)
    Health and Status Branch Coverage
      ✓ should cover both branches of health determination (2 ms)
      ✓ should cover both branches of active validation count check (3 ms)
    Error Handling Branch Coverage
      ✓ should cover both branches of error recovery logic (6 ms)
      ✕ should cover both branches of retry logic (39 ms)
      ✕ should cover both branches of timeout handling (30005 ms)
    Complex Conditional Branch Coverage
      ✕ should cover all branches of test history filtering logic (8 ms)
      ✓ should cover all branches of validation scope checking (8 ms)

  ● MemorySafetyIntegrationValidator - Branch Coverage Tests › Test Execution Branch Coverage › should cover both branches of test execution error handling

    expect(received).toBe(expected) // Object.is equality

    Expected: false
    Received: true

      407 |
      408 |       const failResult = await validator.executeMemorySafetyTest(invalidTest);
    > 409 |       expect(failResult.success).toBe(false);
          |                                  ^
      410 |       expect(failResult.metadata.error).toBeDefined();
      411 |     });
      412 |   });

      at Object.<anonymous> (server/src/platform/integration/testing-framework/__tests__/MemorySafetyIntegrationValidator.branch.test.ts:409:34)

  ● MemorySafetyIntegrationValidator - Branch Coverage Tests › Error Handling Branch Coverage › should cover both branches of retry logic

    expect(received).toBe(expected) // Object.is equality

    Expected: true
    Received: false

      524 |
      525 |       const retryResult = await validator.executeMemorySafetyTest(mockTestSuite.memorySafetyTests[0]);
    > 526 |       expect(retryResult.success).toBe(true);
          |                                   ^
      527 |       expect(attemptCount).toBe(2);
      528 |
      529 |       // Restore original method

      at Object.<anonymous> (server/src/platform/integration/testing-framework/__tests__/MemorySafetyIntegrationValidator.branch.test.ts:526:35)

  ● MemorySafetyIntegrationValidator - Branch Coverage Tests › Error Handling Branch Coverage › should cover both branches of timeout handling

    thrown: "Exceeded timeout of 30000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      531 |     });
      532 |
    > 533 |     it('should cover both branches of timeout handling', async () => {
          |     ^
      534 |       await validator.initialize();
      535 |
      536 |       // Branch 1: No timeout (false branch)

      at server/src/platform/integration/testing-framework/__tests__/MemorySafetyIntegrationValidator.branch.test.ts:533:5
      at server/src/platform/integration/testing-framework/__tests__/MemorySafetyIntegrationValidator.branch.test.ts:460:3
      at Object.<anonymous> (server/src/platform/integration/testing-framework/__tests__/MemorySafetyIntegrationValidator.branch.test.ts:32:1)

  ● MemorySafetyIntegrationValidator - Branch Coverage Tests › Complex Conditional Branch Coverage › should cover all branches of test history filtering logic

    expect(received).toBe(expected) // Object.is equality

    Expected: 2
    Received: 0

      651 |
      652 |       history = await validator.getMemorySafetyTestHistory();
    > 653 |       expect(history.testResults.length).toBe(2); // Should keep all remaining
          |                                          ^
      654 |     });
      655 |
      656 |     it('should cover all branches of validation scope checking', async () => {

      at Object.<anonymous> (server/src/platform/integration/testing-framework/__tests__/MemorySafetyIntegrationValidator.branch.test.ts:653:42)

Test Suites: 1 failed, 1 total
Tests:       4 failed, 11 passed, 15 total
Snapshots:   0 total
Time:        30.705 s
Ran all test suites matching /MemorySafetyIntegrationValidator\.branch\.test\.ts/i.
