 FAIL  server/src/platform/integration/testing-framework/__tests__/MemorySafetyIntegrationValidator.branch.test.ts (70 MB heap size)
  MemorySafetyIntegrationValidator - Branch Coverage Tests
    Initialization Branch Coverage
      ✕ should cover both branches of validator initialization check (12 ms)
      ✓ should cover both branches of validation active check (8 ms)
      ✓ should cover both branches of monitoring enabled check (4 ms)
    Configuration Validation Branch Coverage
      ✓ should cover both branches of configuration validation (4 ms)
      ✓ should cover both branches of test type validation (3 ms)
      ✓ should cover both branches of test already enabled check (4 ms)
    Test Execution Branch Coverage
      ✕ should cover both branches of test suite validation (5 ms)
      ✕ should cover both branches of test execution error handling (4 ms)
    Health and Status Branch Coverage
      ✓ should cover both branches of health determination (5 ms)
      ✓ should cover both branches of active validation count check (4 ms)
    Error Handling Branch Coverage
      ✓ should cover both branches of error recovery logic (3 ms)
      ✕ should cover both branches of retry logic (7 ms)
      ✕ should cover both branches of timeout handling (3 ms)
    Complex Conditional Branch Coverage
      ✕ should cover all branches of test history filtering logic (4 ms)
      ✕ should cover all branches of validation scope checking (4 ms)

  ● MemorySafetyIntegrationValidator - Branch Coverage Tests › Initialization Branch Coverage › should cover both branches of validator initialization check

    expect(received).toBe(expected) // Object.is equality

    Expected: false
    Received: true

      236 |       // Branch 1: Validator not initialized (false branch)
      237 |       const uninitializedResult = await validator.startMemorySafetyValidation();
    > 238 |       expect(uninitializedResult.success).toBe(false);
          |                                           ^
      239 |       expect(uninitializedResult.metadata.error).toContain('not initialized');
      240 |
      241 |       // Branch 2: Validator initialized (true branch)

      at Object.<anonymous> (server/src/platform/integration/testing-framework/__tests__/MemorySafetyIntegrationValidator.branch.test.ts:238:43)

  ● MemorySafetyIntegrationValidator - Branch Coverage Tests › Test Execution Branch Coverage › should cover both branches of test suite validation

    expect(received).toBeDefined()

    Received: undefined

      373 |       const invalidResult = await validator.validateMemorySafety(invalidTestSuite);
      374 |       expect(invalidResult.success).toBe(false);
    > 375 |       expect(invalidResult.metadata.error).toBeDefined();
          |                                            ^
      376 |     });
      377 |
      378 |     it('should cover both branches of test execution error handling', async () => {

      at Object.<anonymous> (server/src/platform/integration/testing-framework/__tests__/MemorySafetyIntegrationValidator.branch.test.ts:375:44)

  ● MemorySafetyIntegrationValidator - Branch Coverage Tests › Test Execution Branch Coverage › should cover both branches of test execution error handling

    expect(received).toBe(expected) // Object.is equality

    Expected: false
    Received: true

      407 |
      408 |       const failResult = await validator.executeMemorySafetyTest(invalidTest);
    > 409 |       expect(failResult.success).toBe(false);
          |                                  ^
      410 |       expect(failResult.metadata.error).toBeDefined();
      411 |     });
      412 |   });

      at Object.<anonymous> (server/src/platform/integration/testing-framework/__tests__/MemorySafetyIntegrationValidator.branch.test.ts:409:34)

  ● MemorySafetyIntegrationValidator - Branch Coverage Tests › Error Handling Branch Coverage › should cover both branches of retry logic

    expect(received).toBe(expected) // Object.is equality

    Expected: 2
    Received: 0

      525 |       const retryResult = await validator.executeMemorySafetyTest(mockTestSuite.memorySafetyTests[0]);
      526 |       expect(retryResult.success).toBe(true);
    > 527 |       expect(attemptCount).toBe(2);
          |                            ^
      528 |
      529 |       // Restore original method
      530 |       (validator as any)._executeMemorySafetyTestInternal = originalExecuteTest;

      at Object.<anonymous> (server/src/platform/integration/testing-framework/__tests__/MemorySafetyIntegrationValidator.branch.test.ts:527:28)

  ● MemorySafetyIntegrationValidator - Branch Coverage Tests › Error Handling Branch Coverage › should cover both branches of timeout handling

    expect(received).toBe(expected) // Object.is equality

    Expected: false
    Received: true

      571 |
      572 |       const timeoutResult = await validator.executeMemorySafetyTest(mockTestSuite.memorySafetyTests[0]);
    > 573 |       expect(timeoutResult.success).toBe(false);
          |                                     ^
      574 |
      575 |       // Restore original method
      576 |       (validator as any)._executeMemorySafetyTestInternal = originalExecuteTest;

      at Object.<anonymous> (server/src/platform/integration/testing-framework/__tests__/MemorySafetyIntegrationValidator.branch.test.ts:573:37)

  ● MemorySafetyIntegrationValidator - Branch Coverage Tests › Complex Conditional Branch Coverage › should cover all branches of test history filtering logic

    expect(received).toBe(expected) // Object.is equality

    Expected: 2
    Received: 0

      651 |
      652 |       history = await validator.getMemorySafetyTestHistory();
    > 653 |       expect(history.testResults.length).toBe(2); // Should keep all remaining
          |                                          ^
      654 |     });
      655 |
      656 |     it('should cover all branches of validation scope checking', async () => {

      at Object.<anonymous> (server/src/platform/integration/testing-framework/__tests__/MemorySafetyIntegrationValidator.branch.test.ts:653:42)

  ● MemorySafetyIntegrationValidator - Branch Coverage Tests › Complex Conditional Branch Coverage › should cover all branches of validation scope checking

    expect(received).toBe(expected) // Object.is equality

    Expected: false
    Received: true

      716 |       };
      717 |       const outOfScopeResult = await validator.executeMemorySafetyTest(outOfScopeTest);
    > 718 |       expect(outOfScopeResult.success).toBe(false);
          |                                        ^
      719 |     });
      720 |   });
      721 | });

      at Object.<anonymous> (server/src/platform/integration/testing-framework/__tests__/MemorySafetyIntegrationValidator.branch.test.ts:718:40)

Test Suites: 1 failed, 1 total
Tests:       7 failed, 8 passed, 15 total
Snapshots:   0 total
Time:        0.735 s, estimated 1 s
Ran all test suites matching /MemorySafetyIntegrationValidator.branch.test.ts/i.
