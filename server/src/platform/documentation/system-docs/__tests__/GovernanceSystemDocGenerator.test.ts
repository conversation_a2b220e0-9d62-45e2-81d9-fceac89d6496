/**
 * @file GovernanceSystemDocGenerator Tests
 * @filepath server/src/platform/documentation/system-docs/__tests__/GovernanceSystemDocGenerator.test.ts
 * @task-id D-TSK-01.SUB-01.1.IMP-01
 * @component governance-system-doc-generator-tests
 * @reference foundation-context.DOCUMENTATION.001.TESTS
 * @tier T1
 * @context foundation-context
 * @category Documentation-Services-Tests
 * @created 2025-09-06
 * @modified 2025-09-06
 */

import { GovernanceSystemDocGenerator } from '../GovernanceSystemDocGenerator';
import {
  IGovernanceSystemContext,
  IGovernanceArchitectureContext,
  IGovernanceComplianceContext,
  IGovernanceOperationalContext
} from '../../../../../../shared/src/interfaces/governance/management-configuration/governance-rule-documentation-generator';
import {
  TGovernanceSystemDocGeneratorConfig,
  TDocumentationGenerationOptions
} from '../../../../../../shared/src/types/platform/governance/management-configuration/documentation-generator-types';

// Mock the resilient timing infrastructure
jest.mock('../../../../../../shared/src/base/utils/ResilientTiming', () => ({
  ResilientTimer: jest.fn().mockImplementation(() => ({
    start: jest.fn().mockReturnValue({
      end: jest.fn().mockReturnValue({ duration: 100, success: true })
    })
  }))
}));

jest.mock('../../../../../../shared/src/base/utils/ResilientMetrics', () => ({
  ResilientMetricsCollector: jest.fn().mockImplementation(() => ({
    recordValue: jest.fn(),
    recordTiming: jest.fn()
  }))
}));

describe('GovernanceSystemDocGenerator', () => {
  let generator: GovernanceSystemDocGenerator;
  let mockConfig: Partial<TGovernanceSystemDocGeneratorConfig>;

  beforeEach(() => {
    mockConfig = {
      generatorId: 'test-generator',
      generatorName: 'Test Generator',
      version: '1.0.0',
      outputConfig: {
        defaultFormat: 'markdown',
        outputDirectory: './test-output',
        fileNamingConvention: 'test-{timestamp}',
        includeTableOfContents: true,
        includeIndex: true,
        includeGlossary: false,
        includeAppendices: false
      },
      generationOptions: {
        parallelProcessing: true,
        maxParallelTasks: 2,
        generationTimeout: 30000,
        includeTimestamps: true,
        includeVersionInformation: true,
        includeGenerationMetadata: true,
        validateOutput: true
      }
    };

    generator = new GovernanceSystemDocGenerator(mockConfig);
  });

  afterEach(async () => {
    if (generator) {
      await generator.shutdown();
    }
  });

  describe('Initialization', () => {
    test('should create generator with default configuration', () => {
      const defaultGenerator = new GovernanceSystemDocGenerator();
      expect(defaultGenerator).toBeInstanceOf(GovernanceSystemDocGenerator);
    });

    test('should create generator with custom configuration', () => {
      expect(generator).toBeInstanceOf(GovernanceSystemDocGenerator);
    });

    test('should initialize successfully', async () => {
      await expect(generator.initialize()).resolves.not.toThrow();
    });

    test('should shutdown successfully', async () => {
      await generator.initialize();
      await expect(generator.shutdown()).resolves.not.toThrow();
    });
  });



  describe('Validation', () => {
    test('should validate successfully with proper configuration', async () => {
      await generator.initialize();
      const result = await generator.validate();
      
      expect(result).toBeDefined();
      expect(result.timestamp).toBeDefined();
      expect(Array.isArray(result.errors)).toBe(true);
      expect(Array.isArray(result.warnings)).toBe(true);
    });

    test('should return validation errors for invalid configuration', async () => {
      const invalidGenerator = new GovernanceSystemDocGenerator({
        generatorId: '', // Invalid empty ID
        outputConfig: undefined as any
      });

      await invalidGenerator.initialize();
      const result = await invalidGenerator.validate();

      expect(result.status).toBe('invalid');
      expect(result.errors.length).toBeGreaterThan(0);

      await invalidGenerator.shutdown();
    });
  });

  describe('Capabilities', () => {
    test('should return correct capabilities', async () => {
      await generator.initialize();
      const capabilities = await generator.getCapabilities();
      
      expect(capabilities.supportedFormats).toContain('markdown');
      expect(capabilities.supportedFormats).toContain('html');
      expect(capabilities.supportedFormats).toContain('pdf');
      expect(capabilities.supportedFormats).toContain('json');
      
      expect(capabilities.templateSupport).toBe(true);
      expect(capabilities.batchProcessingSupport).toBe(true);
      expect(capabilities.customFormattingSupport).toBe(true);
    });
  });

  describe('System Documentation Generation', () => {
    let mockSystemContext: IGovernanceSystemContext;

    beforeEach(() => {
      mockSystemContext = {
        id: 'test-system',
        name: 'Test Governance System',
        version: '1.0.0',
        description: 'Test system for documentation generation',
        components: [
          {
            id: 'component-1',
            name: 'Test Component',
            type: 'service',
            version: '1.0.0'
          }
        ],
        configuration: {
          id: 'config-1',
          parameters: {},
          environment: {},
          security: {}
        },
        metadata: {
          created: new Date().toISOString(),
          modified: new Date().toISOString(),
          creator: 'test-user'
        }
      };
    });

    test('should generate system documentation successfully', async () => {
      await generator.initialize();
      
      const options: TDocumentationGenerationOptions = {
        format: 'markdown',
        includeTableOfContents: true
      };

      const result = await generator.generateSystemDocumentation(mockSystemContext, options);
      
      expect(result).toBeDefined();
      expect(result.id).toBeDefined();
      expect(result.context).toBe(mockSystemContext.id);
      expect(result.format).toBe('markdown');
      expect(result.content).toBeDefined();
      expect(result.content.length).toBeGreaterThan(0);
      expect(result.metadata).toBeDefined();
      expect(result.auditTrail).toBeDefined();
      expect(result.auditTrail.length).toBeGreaterThan(0);
    });

    test('should generate system documentation with different formats', async () => {
      await generator.initialize();
      
      const formats = ['markdown', 'html', 'json'] as const;
      
      for (const format of formats) {
        const result = await generator.generateSystemDocumentation(
          mockSystemContext,
          { format }
        );
        
        expect(result.format).toBe(format);
        expect(result.content).toBeDefined();
      }
    });
  });

  describe('Architecture Documentation Generation', () => {
    let mockArchitectureContext: IGovernanceArchitectureContext;

    beforeEach(() => {
      mockArchitectureContext = {
        id: 'test-architecture',
        name: 'Test Architecture',
        version: '1.0.0',
        patterns: [
          {
            id: 'pattern-1',
            name: 'Test Pattern',
            type: 'architectural',
            description: 'Test architectural pattern'
          }
        ],
        designDecisions: [
          {
            id: 'decision-1',
            title: 'Test Decision',
            description: 'Test design decision',
            rationale: 'Test rationale',
            status: 'approved',
            date: new Date().toISOString()
          }
        ],
        structure: {
          id: 'structure-1',
          layers: [],
          modules: [],
          interfaces: [],
          relationships: []
        },
        metadata: {
          version: '1.0.0',
          status: 'active',
          owner: 'test-user'
        }
      };
    });

    test('should generate architecture documentation successfully', async () => {
      await generator.initialize();
      
      const result = await generator.generateArchitectureDocumentation(
        mockArchitectureContext,
        { format: 'markdown' }
      );
      
      expect(result).toBeDefined();
      expect(result.context).toBe(mockArchitectureContext.id);
      expect(result.content).toBeDefined();
      expect(result.content.length).toBeGreaterThan(0);
    });
  });

  describe('Batch Documentation Generation', () => {
    test('should generate batch documentation successfully', async () => {
      await generator.initialize();
      
      const contexts = [
        {
          id: 'context-1',
          name: 'Context 1',
          version: '1.0.0',
          components: [],
          configuration: { id: 'config-1', parameters: {}, environment: {}, security: {} },
          metadata: { created: new Date().toISOString(), modified: new Date().toISOString(), creator: 'test' }
        },
        {
          id: 'context-2',
          name: 'Context 2',
          version: '1.0.0',
          components: [],
          configuration: { id: 'config-2', parameters: {}, environment: {}, security: {} },
          metadata: { created: new Date().toISOString(), modified: new Date().toISOString(), creator: 'test' }
        }
      ];

      const results = await generator.generateBatchDocumentation(
        contexts,
        { format: 'markdown', concurrency: 2 }
      );
      
      expect(results).toBeDefined();
      expect(results.length).toBe(2);
      expect(results[0].content).toBeDefined();
      expect(results[1].content).toBeDefined();
    });

    test('should handle batch generation with concurrency limits', async () => {
      await generator.initialize();
      
      const contexts = Array.from({ length: 5 }, (_, i) => ({
        id: `context-${i}`,
        name: `Context ${i}`,
        version: '1.0.0',
        components: [],
        configuration: { id: `config-${i}`, parameters: {}, environment: {}, security: {} },
        metadata: { created: new Date().toISOString(), modified: new Date().toISOString(), creator: 'test' }
      }));

      const results = await generator.generateBatchDocumentation(
        contexts,
        { format: 'markdown', concurrency: 2 }
      );
      
      expect(results.length).toBe(5);
    });
  });

  describe('Error Handling', () => {
    test('should handle invalid context gracefully', async () => {
      await generator.initialize();
      
      const invalidContext = {} as any;
      
      await expect(
        generator.generate(invalidContext, { format: 'markdown' })
      ).rejects.toThrow('Unsupported context type');
    });

    test('should handle generation errors gracefully', async () => {
      await generator.initialize();

      // Test with malformed context that should be handled gracefully
      const malformedContext = {
        id: null, // Invalid ID
        name: undefined,
        version: ''
      } as any;

      const result = await generator.generateSystemDocumentation(malformedContext, {});

      // Should handle gracefully and return a result
      expect(result).toBeDefined();
      expect(result.id).toBeDefined();
      expect(result.content).toBeDefined();
    });
  });

  describe('Output Validation', () => {
    test('should validate output successfully', async () => {
      await generator.initialize();

      const validOutput = {
        id: 'test-output',
        context: 'test-context',
        format: 'markdown',
        content: '# Test Documentation\n\nThis is test content.',
        metadata: {
          contextId: 'test-context',
          generatedAt: new Date().toISOString(),
          format: 'markdown',
          version: '1.0.0',
          authority: 'test',
          complianceLevel: 'standard',
          securityLevel: 'standard',
          rulesCount: 1,
          sectionsCount: 1,
          validationStatus: 'validated',
          auditTrail: []
        },
        generatedAt: new Date().toISOString(),
        version: '1.0.0',
        auditTrail: []
      };

      const validation = await generator.validateOutput(validOutput);

      expect(validation.status).toBe('passed');
      expect(validation.id).toBeDefined();
    });

    test('should detect invalid output', async () => {
      await generator.initialize();

      const invalidOutput = {
        id: '',
        context: '',
        format: 'invalid-format',
        content: '',
        metadata: {} as any,
        generatedAt: '',
        version: '',
        auditTrail: []
      };

      const validation = await generator.validateOutput(invalidOutput);

      expect(validation.status).toBe('failed');
      expect(validation.id).toBeDefined();
    });

    test('should handle validation errors gracefully', async () => {
      await generator.initialize();

      // Mock the timing.end to throw an error to test error handling
      const mockTimer = {
        end: jest.fn().mockImplementation(() => {
          throw new Error('Timing error');
        })
      };

      // Mock the _resilientTimer.start to return our mock timer
      (generator as any)._resilientTimer.start = jest.fn().mockReturnValue(mockTimer);

      const validOutput = {
        id: 'test-output',
        context: 'test-context',
        format: 'markdown',
        content: 'Test content',
        metadata: {
          contextId: 'test-context',
          generatedAt: new Date().toISOString(),
          format: 'markdown',
          version: '1.0.0',
          authority: 'test',
          complianceLevel: 'standard',
          securityLevel: 'standard',
          rulesCount: 1,
          sectionsCount: 1,
          validationStatus: 'validated',
          auditTrail: []
        },
        generatedAt: new Date().toISOString(),
        version: '1.0.0',
        auditTrail: []
      };

      await expect(generator.validateOutput(validOutput)).rejects.toThrow('Timing error');
    });
  });

  describe('Compliance Documentation Generation', () => {
    test('should generate compliance documentation successfully', async () => {
      await generator.initialize();

      const complianceContext = {
        id: 'compliance-context-001',
        framework: 'SOX',
        version: '2.0.0',
        requirements: [
          {
            id: 'req-001',
            title: 'Financial Reporting Accuracy',
            description: 'Financial reporting accuracy',
            priority: 'high',
            category: 'financial'
          },
          {
            id: 'req-002',
            title: 'Internal Controls Documentation',
            description: 'Internal controls documentation',
            priority: 'medium',
            category: 'controls'
          }
        ],
        standards: [
          {
            id: 'std-001',
            name: 'SOX Standard',
            version: '2.0.0',
            description: 'Sarbanes-Oxley compliance',
            requirements: ['req-001', 'req-002']
          }
        ],
        validationCriteria: [
          {
            id: 'val-001',
            name: 'Financial accuracy',
            description: 'Validation criteria for financial accuracy requirements',
            type: 'financial',
            rules: ['accuracy-check', 'completeness-check'],
            threshold: 99.9
          }
        ],
        metadata: {
          frameworkVersion: '2.0.0',
          status: 'active',
          lastAssessment: '2024-01-01',
          nextAssessment: '2024-12-31',
          officer: 'compliance-team'
        }
      };

      const result = await generator.generateComplianceDocumentation(complianceContext, {
        format: 'markdown'
      });

      expect(result).toBeDefined();
      expect(result.id).toBeDefined();
      expect(result.context).toBe('compliance-context-001');
      expect(result.format).toBe('markdown');
      expect(result.content).toContain('SOX');
      expect(result.content).toContain('2.0.0');
      expect(result.metadata.contextId).toBe('compliance-context-001');
      expect(result.auditTrail).toHaveLength(1);
      expect(result.auditTrail[0].action).toBe('generate_compliance_documentation');
    });

    test('should generate compliance documentation with HTML format', async () => {
      await generator.initialize();

      const complianceContext = {
        id: 'compliance-context-002',
        framework: 'GDPR',
        version: '1.0.0',
        requirements: [
          { id: 'req-001', title: 'Data Protection', description: 'Data protection requirement', priority: 'high', category: 'privacy' }
        ],
        standards: [
          { id: 'std-001', name: 'GDPR Standard', version: '1.0.0', description: 'General Data Protection Regulation', requirements: ['data-protection', 'consent-management'] }
        ],
        validationCriteria: [
          { id: 'val-001', name: 'Data protection compliance', description: 'Validate data protection measures', type: 'compliance', rules: ['gdpr-rule-1', 'gdpr-rule-2'], threshold: 100 }
        ],
        metadata: {
          frameworkVersion: '1.0.0',
          status: 'active',
          officer: 'privacy-team'
        }
      };

      const result = await generator.generateComplianceDocumentation(complianceContext, {
        format: 'html'
      });

      expect(result.format).toBe('html');
      expect(result.content).toContain('<html><body>');
      expect(result.content).toContain('</body></html>');
      expect(result.content).toContain('GDPR');
    });

    test('should handle compliance documentation with minimal context', async () => {
      await generator.initialize();

      const minimalContext = {
        id: 'minimal-compliance',
        framework: 'ISO27001',
        version: '3.0.0',
        requirements: [], // Empty requirements array
        standards: [],
        validationCriteria: [],
        metadata: {
          frameworkVersion: '3.0.0',
          status: 'active',
          officer: 'security-team'
        }
      };

      const result = await generator.generateComplianceDocumentation(minimalContext, {});

      expect(result).toBeDefined();
      expect(result.content).toContain('ISO27001');
      expect(result.content).toContain('0'); // Should show 0 requirements
    });
  });

  describe('Operational Documentation Generation', () => {
    test('should generate operational documentation successfully', async () => {
      await generator.initialize();

      const operationalContext = {
        id: 'operational-context-001',
        scope: 'Production Operations',
        version: '1.5.0',
        procedures: [
          {
            id: 'proc-001',
            name: 'Deployment procedure',
            description: 'Standard deployment process',
            steps: [
              { id: 'step-1', name: 'Pre-deployment check', description: 'Verify system readiness', order: 1 },
              { id: 'step-2', name: 'Deploy application', description: 'Execute deployment', order: 2 }
            ]
          },
          {
            id: 'proc-002',
            name: 'Rollback procedure',
            description: 'Emergency rollback process',
            steps: [
              { id: 'step-1', name: 'Identify issue', description: 'Determine rollback need', order: 1 }
            ]
          }
        ],
        workflows: [
          {
            id: 'wf-001',
            name: 'CI/CD Workflow',
            description: 'Continuous integration and deployment',
            stages: [
              { id: 'stage-1', name: 'Build', description: 'Build application', order: 1, actions: ['compile', 'test'] }
            ]
          }
        ],
        maintenanceGuidelines: [
          {
            id: 'mg-001',
            title: 'Daily Maintenance',
            description: 'Daily system maintenance tasks',
            frequency: 'daily',
            procedures: ['check-logs', 'monitor-performance']
          }
        ],
        metadata: {
          version: '1.5.0',
          status: 'active',
          owner: 'ops-team'
        }
      };

      const result = await generator.generateOperationalDocumentation(operationalContext, {
        format: 'markdown'
      });

      expect(result).toBeDefined();
      expect(result.id).toBeDefined();
      expect(result.context).toBe('operational-context-001');
      expect(result.format).toBe('markdown');
      expect(result.content).toContain('Production Operations');
      expect(result.content).toContain('1.5.0');
      expect(result.content).toContain('2'); // Should show 2 procedures
      expect(result.metadata.contextId).toBe('operational-context-001');
      expect(result.auditTrail).toHaveLength(1);
      expect(result.auditTrail[0].action).toBe('generate_operational_documentation');
    });

    test('should generate operational documentation with HTML format', async () => {
      await generator.initialize();

      const operationalContext = {
        id: 'operational-context-002',
        scope: 'Development Operations',
        version: '2.0.0',
        procedures: [],
        workflows: [],
        maintenanceGuidelines: [],
        metadata: {
          version: '2.0.0',
          status: 'active',
          owner: 'dev-team'
        }
      };

      const result = await generator.generateOperationalDocumentation(operationalContext, {
        format: 'html'
      });

      expect(result.format).toBe('html');
      expect(result.content).toContain('<html><body>');
      expect(result.content).toContain('</body></html>');
      expect(result.content).toContain('Development Operations');
    });

    test('should handle operational documentation with minimal context', async () => {
      await generator.initialize();

      const minimalContext = {
        id: 'minimal-operational-context',
        scope: 'Test Operations',
        version: '1.0.0',
        procedures: [], // Empty procedures array
        workflows: [],
        maintenanceGuidelines: [],
        metadata: {
          version: '1.0.0',
          status: 'active',
          owner: 'test-team'
        }
      };

      const result = await generator.generateOperationalDocumentation(minimalContext, {});

      expect(result).toBeDefined();
      expect(result.content).toContain('Test Operations');
      expect(result.content).toContain('0'); // Should show 0 procedures
    });
  });

  describe('Error Handling and Edge Cases', () => {
    test('should handle generation errors in system documentation', async () => {
      await generator.initialize();

      // Mock the timing.end to throw an error to test error handling
      const mockTimer = {
        end: jest.fn().mockImplementation(() => {
          throw new Error('System generation timing error');
        })
      };

      // Mock the _resilientTimer.start to return our mock timer
      (generator as any)._resilientTimer.start = jest.fn().mockReturnValue(mockTimer);

      const systemContext = {
        id: 'error-system-context',
        name: 'Error Test System',
        version: '1.0.0',
        description: 'System for testing error handling',
        components: [],
        configuration: {
          id: 'config-001',
          parameters: { param1: 'value1' },
          environment: { env: 'test' },
          security: { level: 'standard' }
        },
        metadata: {
          created: '2024-01-01T00:00:00Z',
          modified: '2024-01-01T00:00:00Z',
          creator: 'test-team'
        }
      };

      await expect(generator.generateSystemDocumentation(systemContext, {})).rejects.toThrow('System generation timing error');
    });

    test('should handle generation errors in architecture documentation', async () => {
      await generator.initialize();

      // Mock the timing.end to throw an error to test error handling
      const mockTimer = {
        end: jest.fn().mockImplementation(() => {
          throw new Error('Architecture generation timing error');
        })
      };

      // Mock the _resilientTimer.start to return our mock timer
      (generator as any)._resilientTimer.start = jest.fn().mockReturnValue(mockTimer);

      const architectureContext = {
        id: 'error-architecture-context',
        name: 'Error Test Architecture',
        version: '1.0.0',
        patterns: [],
        designDecisions: [],
        structure: {
          id: 'structure-001',
          layers: [],
          modules: [],
          interfaces: [],
          relationships: []
        },
        metadata: {
          version: '1.0.0',
          status: 'active',
          owner: 'architecture-team'
        }
      };

      await expect(generator.generateArchitectureDocumentation(architectureContext, {})).rejects.toThrow('Architecture generation timing error');
    });

    test('should handle generation errors in compliance documentation', async () => {
      await generator.initialize();

      // Mock the timing.end to throw an error to test error handling
      const mockTimer = {
        end: jest.fn().mockImplementation(() => {
          throw new Error('Compliance generation timing error');
        })
      };

      // Mock the _resilientTimer.start to return our mock timer
      (generator as any)._resilientTimer.start = jest.fn().mockReturnValue(mockTimer);

      const complianceContext = {
        id: 'error-compliance-context',
        framework: 'Error Framework',
        version: '1.0.0',
        requirements: [],
        standards: [],
        validationCriteria: [],
        metadata: {
          frameworkVersion: '1.0.0',
          status: 'active',
          officer: 'error-team'
        }
      };

      await expect(generator.generateComplianceDocumentation(complianceContext, {})).rejects.toThrow('Compliance generation timing error');
    });

    test('should handle generation errors in operational documentation', async () => {
      await generator.initialize();

      // Mock the timing.end to throw an error to test error handling
      const mockTimer = {
        end: jest.fn().mockImplementation(() => {
          throw new Error('Operational generation timing error');
        })
      };

      // Mock the _resilientTimer.start to return our mock timer
      (generator as any)._resilientTimer.start = jest.fn().mockReturnValue(mockTimer);

      const operationalContext = {
        id: 'error-operational-context',
        scope: 'Error Operations',
        version: '1.0.0',
        procedures: [],
        workflows: [],
        maintenanceGuidelines: [],
        metadata: {
          version: '1.0.0',
          status: 'active',
          owner: 'error-team'
        }
      };

      await expect(generator.generateOperationalDocumentation(operationalContext, {})).rejects.toThrow('Operational generation timing error');
    });
  });

  describe('Batch Documentation Generation', () => {
    test('should generate multiple documents concurrently', async () => {
      await generator.initialize();

      const contexts = [
        {
          id: 'batch-system-1',
          name: 'Batch System 1',
          version: '1.0.0',
          description: 'First batch system',
          components: [],
          configuration: {
            id: 'config-batch-1',
            parameters: { param1: 'value1' },
            environment: { env: 'batch' },
            security: { level: 'standard' }
          },
          metadata: {
            created: '2024-01-01T00:00:00Z',
            modified: '2024-01-01T00:00:00Z',
            creator: 'batch-team'
          }
        },
        {
          id: 'batch-system-2',
          name: 'Batch System 2',
          version: '2.0.0',
          description: 'Second batch system',
          components: [],
          configuration: {
            id: 'config-batch-2',
            parameters: { param2: 'value2' },
            environment: { env: 'batch' },
            security: { level: 'high' }
          },
          metadata: {
            created: '2024-01-02T00:00:00Z',
            modified: '2024-01-02T00:00:00Z',
            creator: 'batch-team'
          }
        }
      ];

      const results = await generator.generateBatchDocumentation(contexts, {
        format: 'markdown',
        concurrency: 2
      });

      expect(results).toHaveLength(2);
      expect(results[0].id).toBeDefined();
      expect(results[1].id).toBeDefined();
      expect(results[0].content).toContain('Batch System 1');
      expect(results[1].content).toContain('Batch System 2');
    });

    test('should handle batch generation with different concurrency limits', async () => {
      await generator.initialize();

      const contexts = Array.from({ length: 5 }, (_, i) => ({
        id: `concurrent-system-${i + 1}`,
        name: `Concurrent System ${i + 1}`,
        version: '1.0.0',
        description: `System ${i + 1} for concurrency testing`,
        components: [],
        configuration: {
          id: `config-concurrent-${i + 1}`,
          parameters: { index: i + 1 },
          environment: { env: 'concurrent' },
          security: { level: 'standard' }
        },
        metadata: {
          created: '2024-01-01T00:00:00Z',
          modified: '2024-01-01T00:00:00Z',
          creator: 'concurrent-team'
        }
      }));

      const results = await generator.generateBatchDocumentation(contexts, {
        format: 'markdown',
        concurrency: 1 // Test with low concurrency
      });

      expect(results).toHaveLength(5);
      results.forEach((result, index) => {
        expect(result.content).toContain(`Concurrent System ${index + 1}`);
      });
    });

    test('should handle empty batch generation', async () => {
      await generator.initialize();

      const results = await generator.generateBatchDocumentation([], {
        format: 'markdown'
      });

      expect(results).toHaveLength(0);
    });
  });

  describe('Template Processing and Queue Management', () => {
    test('should process templates correctly', async () => {
      await generator.initialize();

      // Test template processing by accessing private methods through public APIs
      const systemContext = {
        id: 'template-test-system',
        name: 'Template Test System',
        version: '1.0.0',
        description: 'System for testing template processing',
        components: [
          {
            id: 'comp-1',
            name: 'Component 1',
            type: 'service',
            version: '1.0.0',
            description: 'Test component'
          }
        ],
        configuration: {
          id: 'config-template',
          parameters: { templateParam: 'templateValue' },
          environment: { env: 'template' },
          security: { level: 'standard' }
        },
        metadata: {
          created: '2024-01-01T00:00:00Z',
          modified: '2024-01-01T00:00:00Z',
          creator: 'template-team'
        }
      };

      const result = await generator.generateSystemDocumentation(systemContext, {
        format: 'markdown'
      });

      // Verify template processing worked correctly
      expect(result.content).toContain('Template Test System');
      expect(result.content).toContain('1.0.0');
      expect(result.content).toContain('1'); // Component count
      expect(result.content).toContain(new Date().getFullYear().toString()); // Generation timestamp
    });

    test('should handle queue management with multiple concurrent requests', async () => {
      await generator.initialize();

      // Create multiple concurrent requests to test queue management
      const promises = Array.from({ length: 10 }, (_, i) => {
        const context = {
          id: `queue-system-${i}`,
          name: `Queue System ${i}`,
          version: '1.0.0',
          description: `System ${i} for queue testing`,
          components: [],
          configuration: {
            id: `config-queue-${i}`,
            parameters: { queueIndex: i },
            environment: { env: 'queue' },
            security: { level: 'standard' }
          },
          metadata: {
            created: '2024-01-01T00:00:00Z',
            modified: '2024-01-01T00:00:00Z',
            creator: 'queue-team'
          }
        };

        return generator.generateSystemDocumentation(context, { format: 'markdown' });
      });

      const results = await Promise.all(promises);

      expect(results).toHaveLength(10);
      results.forEach((result, index) => {
        expect(result.content).toContain(`Queue System ${index}`);
      });
    });

    test('should handle different output formats correctly', async () => {
      await generator.initialize();

      const systemContext = {
        id: 'format-test-system',
        name: 'Format Test System',
        version: '1.0.0',
        description: 'System for testing output formats',
        components: [],
        configuration: {
          id: 'config-format',
          parameters: { formatParam: 'formatValue' },
          environment: { env: 'format' },
          security: { level: 'standard' }
        },
        metadata: {
          created: '2024-01-01T00:00:00Z',
          modified: '2024-01-01T00:00:00Z',
          creator: 'format-team'
        }
      };

      // Test different formats
      const markdownResult = await generator.generateSystemDocumentation(systemContext, { format: 'markdown' });
      const htmlResult = await generator.generateSystemDocumentation(systemContext, { format: 'html' });
      const jsonResult = await generator.generateSystemDocumentation(systemContext, { format: 'json' });

      expect(markdownResult.format).toBe('markdown');
      expect(htmlResult.format).toBe('html');
      expect(jsonResult.format).toBe('json');

      // HTML should contain HTML tags
      expect(htmlResult.content).toContain('<html><body>');
      expect(htmlResult.content).toContain('</body></html>');

      // All should contain the system name
      expect(markdownResult.content).toContain('Format Test System');
      expect(htmlResult.content).toContain('Format Test System');
      expect(jsonResult.content).toContain('Format Test System');
    });
  });

  describe('Configuration and Capabilities', () => {
    test('should return correct capabilities', async () => {
      await generator.initialize();

      const capabilities = await generator.getCapabilities();

      expect(capabilities).toBeDefined();
      expect(capabilities.supportedFormats).toContain('markdown');
      expect(capabilities.supportedFormats).toContain('html');
      expect(capabilities.supportedFormats).toContain('pdf');
      expect(capabilities.supportedFormats).toContain('json');
      expect(capabilities.templateSupport).toBe(true);
      expect(capabilities.batchProcessingSupport).toBe(true);
      expect(capabilities.maxDocumentSize).toBeGreaterThan(0);
    });

    test('should handle different generator configurations', async () => {
      const customGenerator = new GovernanceSystemDocGenerator({
        generatorId: 'custom-generator',
        generatorName: 'Custom Generator',
        version: '1.0.0',
        outputConfig: {
          defaultFormat: 'markdown',
          outputDirectory: './custom-output',
          fileNamingConvention: 'custom-{id}-{timestamp}',
          includeTableOfContents: true,
          includeIndex: true,
          includeGlossary: true,
          includeAppendices: true
        },
        templateConfig: {
          defaultTemplate: 'default',
          templateDirectory: './templates',
          customTemplates: {},
          templateVariables: {},
          templateInheritance: true,
          templateValidation: true,
          templateCaching: true
        },
        systemDocSettings: {
          includeSystemOverview: true,
          includeComponentDetails: true,
          includeConfigurationDetails: true,
          includeDependencyMapping: true,
          includeSecurityInformation: true,
          includePerformanceMetrics: true,
          detailLevel: 'comprehensive'
        },
        architectureDocSettings: {
          includeArchitectureOverview: true,
          includeDesignPatterns: true,
          includeDesignDecisions: true,
          includeSystemStructure: true,
          includeQualityAttributes: true,
          includeConstraints: true,
          detailLevel: 'comprehensive'
        },
        complianceDocSettings: {
          includeComplianceOverview: true,
          includeRequirements: true,
          includeStandards: true,
          includeValidationCriteria: true,
          includeAuditInformation: true,
          includeCertificationDetails: true,
          detailLevel: 'comprehensive'
        },
        operationalDocSettings: {
          includeOperationalOverview: true,
          includeProcedures: true,
          includeWorkflows: true,
          includeMaintenanceGuidelines: true,
          includeEmergencyProcedures: true,
          includeEscalationProcedures: true,
          detailLevel: 'detailed'
        },
        validationSettings: {
          enableValidation: true,
          validationRules: ['structure', 'content', 'format'],
          validationTimeout: 10000,
          strictValidation: true,
          validationReporting: true
        },
        cacheSettings: {
          enableCaching: true,
          cacheSizeLimit: 100 * 1024 * 1024,
          cacheTTL: 3600000,
          cacheStrategy: 'lru',
          cacheCompression: true,
          cachePersistence: false
        },
        securitySettings: {
          enableSecurity: true,
          accessControl: true,
          encryptionSettings: {
            enableEncryption: false,
            encryptionAlgorithm: 'AES-256',
            keyManagement: 'internal',
            encryptionLevel: 'standard'
          },
          authenticationRequirements: [],
          authorizationLevels: ['read', 'write'],
          auditLogging: true
        },
        auditSettings: {
          enableAuditTrail: true,
          auditDetailLevel: 'standard',
          auditRetentionPeriod: 2592000000, // 30 days
          auditCompression: true,
          auditExportFormat: ['json', 'csv']
        },
        generationOptions: {
          parallelProcessing: true,
          maxParallelTasks: 5,
          generationTimeout: 30000,
          includeTimestamps: true,
          includeVersionInformation: true,
          includeGenerationMetadata: true,
          validateOutput: true
        },
        performanceSettings: {
          enablePerformanceMonitoring: true,
          performanceThresholds: {
            generationTime: 30000,
            memoryUsage: 100 * 1024 * 1024
          },
          memoryLimits: {
            maxDocumentSize: 100 * 1024 * 1024
          },
          cpuLimits: {
            maxCpuUsage: 90
          },
          optimizationLevel: 'standard'
        }
      });

      await customGenerator.initialize();

      const systemContext = {
        id: 'custom-config-system',
        name: 'Custom Config System',
        version: '1.0.0',
        description: 'System for testing custom configuration',
        components: [],
        configuration: {
          id: 'config-custom',
          parameters: { customParam: 'customValue' },
          environment: { env: 'custom' },
          security: { level: 'high' }
        },
        metadata: {
          created: '2024-01-01T00:00:00Z',
          modified: '2024-01-01T00:00:00Z',
          creator: 'custom-team'
        }
      };

      const result = await customGenerator.generateSystemDocumentation(systemContext, {});

      expect(result).toBeDefined();
      expect(result.content).toContain('Custom Config System');

      await customGenerator.shutdown();
    });

    test('should handle validation with warnings', async () => {
      await generator.initialize();

      // Create a generator with configuration that will generate warnings
      const warningGenerator = new GovernanceSystemDocGenerator({
        generatorId: 'warning-generator',
        outputConfig: undefined as any // This should generate a warning
      });

      await warningGenerator.initialize();

      const result = await warningGenerator.validate();

      expect(result.status).toBe('invalid'); // Should be invalid due to missing output config
      expect(result.errors.length).toBeGreaterThan(0);

      await warningGenerator.shutdown();
    });

    test('should handle memory usage validation', async () => {
      await generator.initialize();

      // Mock high memory usage to test memory validation
      const originalMemoryUsage = process.memoryUsage;
      process.memoryUsage = jest.fn(() => ({
        rss: 100 * 1024 * 1024, // 100MB
        heapTotal: 80 * 1024 * 1024, // 80MB
        heapUsed: 60 * 1024 * 1024, // 60MB - High usage
        external: 10 * 1024 * 1024, // 10MB
        arrayBuffers: 5 * 1024 * 1024 // 5MB
      })) as any;

      const result = await generator.validate();

      expect(result.warnings.length).toBeGreaterThan(0);
      expect(result.warnings.some(warning => warning.includes('Memory usage'))).toBe(true);

      // Restore original function
      process.memoryUsage = originalMemoryUsage;
    });
  });

  describe('Private Method Coverage Through Public APIs', () => {
    test('should exercise template generation methods', async () => {
      await generator.initialize();

      // Test all documentation types to exercise private template methods
      const systemContext = {
        id: 'template-coverage-system',
        name: 'Template Coverage System',
        version: '1.0.0',
        description: 'System for template method coverage',
        components: [],
        configuration: {
          id: 'config-template-coverage',
          parameters: { coverage: true },
          environment: { env: 'coverage' },
          security: { level: 'standard' }
        },
        metadata: {
          created: '2024-01-01T00:00:00Z',
          modified: '2024-01-01T00:00:00Z',
          creator: 'coverage-team'
        }
      };

      const architectureContext = {
        id: 'template-coverage-architecture',
        name: 'Template Coverage Architecture',
        version: '1.0.0',
        patterns: [],
        designDecisions: [],
        structure: {
          id: 'structure-coverage',
          layers: [],
          modules: [],
          interfaces: [],
          relationships: []
        },
        metadata: {
          version: '1.0.0',
          status: 'active',
          owner: 'coverage-team'
        }
      };

      // Generate all types to exercise private methods
      const systemResult = await generator.generateSystemDocumentation(systemContext, { format: 'markdown' });
      const archResult = await generator.generateArchitectureDocumentation(architectureContext, { format: 'html' });

      expect(systemResult.content).toContain('Template Coverage System');
      expect(archResult.content).toContain('Template Coverage Architecture');

      // Verify section counting method is exercised
      expect(systemResult.metadata.sectionsCount).toBeGreaterThan(0);
      expect(archResult.metadata.sectionsCount).toBeGreaterThan(0);
    });

    test('should exercise queue processing with various scenarios', async () => {
      await generator.initialize();

      // Test queue processing with mixed success and validation scenarios
      const contexts = [
        // Valid context
        {
          id: 'queue-valid-system',
          name: 'Valid Queue System',
          version: '1.0.0',
          description: 'Valid system for queue testing',
          components: [],
          configuration: {
            id: 'config-queue-valid',
            parameters: { valid: true },
            environment: { env: 'queue' },
            security: { level: 'standard' }
          },
          metadata: {
            created: '2024-01-01T00:00:00Z',
            modified: '2024-01-01T00:00:00Z',
            creator: 'queue-team'
          }
        },
        // Context with edge case values
        {
          id: 'queue-edge-system',
          name: '', // Empty name to test edge case
          version: '0.0.0',
          description: null as any, // Null description
          components: [],
          configuration: {
            id: 'config-queue-edge',
            parameters: {},
            environment: {},
            security: {}
          },
          metadata: {
            created: '2024-01-01T00:00:00Z',
            modified: '2024-01-01T00:00:00Z',
            creator: 'edge-team'
          }
        }
      ];

      const results = await generator.generateBatchDocumentation(contexts, {
        format: 'markdown',
        concurrency: 2
      });

      expect(results).toHaveLength(2);
      expect(results[0].content).toContain('Valid Queue System');
      expect(results[1].content).toBeDefined(); // Should handle edge case gracefully
    });
  });

  describe('Advanced Coverage Scenarios', () => {
    test('should exercise queue processing with empty queue', async () => {
      await generator.initialize();

      // Access the private method through reflection to test queue processing
      const processQueue = (generator as any)._processGenerationQueue.bind(generator);

      // Test with empty queue
      await processQueue();

      // Should complete without error
      expect(true).toBe(true);
    });

    test('should exercise queue processing with max concurrency reached', async () => {
      await generator.initialize();

      // Mock active tasks to simulate max concurrency
      (generator as any)._activeTasks.add('task1');
      (generator as any)._activeTasks.add('task2');
      (generator as any)._activeTasks.add('task3');

      // Add a task to the queue
      (generator as any)._generationQueue.push({
        context: { id: 'test', name: 'test' },
        options: {},
        resolve: jest.fn(),
        reject: jest.fn()
      });

      const processQueue = (generator as any)._processGenerationQueue.bind(generator);
      await processQueue();

      // Queue should still have the task since max concurrency was reached
      expect((generator as any)._generationQueue.length).toBe(1);

      // Clean up
      (generator as any)._activeTasks.clear();
      (generator as any)._generationQueue.length = 0;
    });

    test('should exercise batch creation with different sizes', async () => {
      await generator.initialize();

      const createBatches = (generator as any)._createBatches.bind(generator);

      // Test with various batch sizes
      const items = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];

      const batches1 = createBatches(items, 3);
      expect(batches1).toHaveLength(4); // [1,2,3], [4,5,6], [7,8,9], [10]
      expect(batches1[0]).toEqual([1, 2, 3]);
      expect(batches1[3]).toEqual([10]);

      const batches2 = createBatches(items, 5);
      expect(batches2).toHaveLength(2); // [1,2,3,4,5], [6,7,8,9,10]

      const batches3 = createBatches([], 3);
      expect(batches3).toHaveLength(0); // Empty array
    });

    test('should exercise section counting with different content types', async () => {
      await generator.initialize();

      const countSections = (generator as any)._countSections.bind(generator);

      // Test with markdown headers
      const contentWithHeaders = `# Main Title
## Section 1
### Subsection 1.1
## Section 2
# Another Main Title`;

      const sectionCount = countSections(contentWithHeaders);
      expect(sectionCount).toBe(5); // 5 headers

      // Test with no headers
      const contentNoHeaders = 'This is plain text without headers';
      const noHeaderCount = countSections(contentNoHeaders);
      expect(noHeaderCount).toBe(1); // Default to 1 section

      // Test with empty content
      const emptyCount = countSections('');
      expect(emptyCount).toBe(1); // Default to 1 section
    });

    test('should exercise context type checking methods', async () => {
      await generator.initialize();

      const isSystemContext = (generator as any)._isGovernanceSystemContext.bind(generator);
      const isArchitectureContext = (generator as any)._isGovernanceArchitectureContext.bind(generator);
      const isComplianceContext = (generator as any)._isGovernanceComplianceContext.bind(generator);
      const isOperationalContext = (generator as any)._isGovernanceOperationalContext.bind(generator);

      // Test system context detection
      const systemContext = { id: 'sys-1', components: [] };
      expect(isSystemContext(systemContext)).toBe(true);
      expect(isSystemContext({ id: 'sys-1' })).toBe(false); // Missing components
      expect(isSystemContext(null)).toBeFalsy(); // null should be falsy

      // Test architecture context detection
      const archContext = { id: 'arch-1', patterns: [] };
      expect(isArchitectureContext(archContext)).toBe(true);
      expect(isArchitectureContext({ id: 'arch-1' })).toBe(false); // Missing patterns

      // Test compliance context detection
      const complianceContext = { id: 'comp-1', requirements: [] };
      expect(isComplianceContext(complianceContext)).toBe(true);
      expect(isComplianceContext({ id: 'comp-1' })).toBe(false); // Missing requirements

      // Test operational context detection
      const operationalContext = { id: 'op-1', procedures: [] };
      expect(isOperationalContext(operationalContext)).toBe(true);
      expect(isOperationalContext({ id: 'op-1' })).toBe(false); // Missing procedures
    });

    test('should exercise performance metrics update methods', async () => {
      await generator.initialize();

      const updateMetrics = (generator as any)._updatePerformanceMetrics.bind(generator);
      const updateGeneratorMetrics = (generator as any)._updateGeneratorPerformanceMetrics.bind(generator);
      const cleanupTasks = (generator as any)._cleanupCompletedTasks.bind(generator);

      // Test performance metrics update
      updateMetrics();

      // Test generator-specific metrics update
      updateGeneratorMetrics();

      // Test task cleanup
      cleanupTasks();

      // Verify metrics were updated
      const generatorData = (generator as any)._generatorData;
      expect(generatorData.performanceMetrics).toBeDefined();
      expect(generatorData.performanceMetrics.totalGenerations).toBeDefined();
    });

    test('should exercise template initialization error handling', async () => {
      const errorGenerator = new GovernanceSystemDocGenerator({
        generatorId: 'error-template-generator',
        generatorName: 'Error Template Generator',
        version: '1.0.0',
        outputConfig: {
          defaultFormat: 'markdown',
          outputDirectory: './output',
          fileNamingConvention: '{id}-{timestamp}',
          includeTableOfContents: true,
          includeIndex: true,
          includeGlossary: true,
          includeAppendices: true
        },
        templateConfig: {
          defaultTemplate: 'nonexistent-template', // This should cause an error
          templateDirectory: './nonexistent-templates',
          customTemplates: {},
          templateVariables: {},
          templateInheritance: true,
          templateValidation: true,
          templateCaching: true
        },
        systemDocSettings: {
          includeSystemOverview: true,
          includeComponentDetails: true,
          includeConfigurationDetails: true,
          includeDependencyMapping: true,
          includeSecurityInformation: true,
          includePerformanceMetrics: true,
          detailLevel: 'standard'
        },
        architectureDocSettings: {
          includeArchitectureOverview: true,
          includeDesignPatterns: true,
          includeDesignDecisions: true,
          includeSystemStructure: true,
          includeQualityAttributes: true,
          includeConstraints: true,
          detailLevel: 'detailed'
        },
        complianceDocSettings: {
          includeComplianceOverview: true,
          includeRequirements: true,
          includeStandards: true,
          includeValidationCriteria: true,
          includeAuditInformation: true,
          includeCertificationDetails: true,
          detailLevel: 'standard'
        },
        operationalDocSettings: {
          includeOperationalOverview: true,
          includeProcedures: true,
          includeWorkflows: true,
          includeMaintenanceGuidelines: true,
          includeEmergencyProcedures: true,
          includeEscalationProcedures: true,
          detailLevel: 'standard'
        },
        generationOptions: {
          parallelProcessing: true,
          maxParallelTasks: 3,
          generationTimeout: 30000,
          includeTimestamps: true,
          includeVersionInformation: true,
          includeGenerationMetadata: true,
          validateOutput: true
        },
        validationSettings: {
          enableValidation: true,
          validationRules: ['structure', 'content'],
          validationTimeout: 10000,
          strictValidation: false,
          validationReporting: true
        },
        performanceSettings: {
          enablePerformanceMonitoring: true,
          performanceThresholds: {
            generationTime: 30000
          },
          memoryLimits: {
            maxDocumentSize: 50 * 1024 * 1024
          },
          cpuLimits: {
            maxCpuUsage: 80
          },
          optimizationLevel: 'standard'
        },
        cacheSettings: {
          enableCaching: true,
          cacheSizeLimit: 50 * 1024 * 1024,
          cacheTTL: 1800000,
          cacheStrategy: 'lru',
          cacheCompression: false,
          cachePersistence: false
        },
        securitySettings: {
          enableSecurity: false,
          accessControl: false,
          encryptionSettings: {
            enableEncryption: false,
            encryptionAlgorithm: 'AES-256',
            keyManagement: 'internal',
            encryptionLevel: 'basic'
          },
          authenticationRequirements: [],
          authorizationLevels: [],
          auditLogging: false
        },
        auditSettings: {
          enableAuditTrail: false,
          auditDetailLevel: 'basic',
          auditRetentionPeriod: 1209600000, // 14 days
          auditCompression: false,
          auditExportFormat: ['json']
        }
      });

      // Mock the template initialization to throw an error
      const originalInitializeTemplates = (errorGenerator as any)._initializeTemplatesCache;
      (errorGenerator as any)._initializeTemplatesCache = jest.fn().mockImplementation(() => {
        throw new Error('Template initialization failed');
      });

      // Should handle template initialization error
      await expect(errorGenerator.initialize()).rejects.toThrow('Template initialization failed');

      // Restore original method
      (errorGenerator as any)._initializeTemplatesCache = originalInitializeTemplates;
    });

    test('should exercise queue processing with task execution', async () => {
      await generator.initialize();

      // Create a mock task with resolve/reject functions
      const mockResolve = jest.fn();
      const mockReject = jest.fn();

      const testContext = {
        id: 'queue-test-system',
        name: 'Queue Test System',
        version: '1.0.0',
        description: 'System for queue testing',
        components: [],
        configuration: {
          id: 'config-queue-test',
          parameters: { test: true },
          environment: { env: 'test' },
          security: { level: 'standard' }
        },
        metadata: {
          created: '2024-01-01T00:00:00Z',
          modified: '2024-01-01T00:00:00Z',
          creator: 'queue-test-team'
        }
      };

      // Add a task to the queue
      (generator as any)._generationQueue.push({
        context: testContext,
        options: { format: 'markdown' },
        resolve: mockResolve,
        reject: mockReject
      });

      // Process the queue
      const processQueue = (generator as any)._processGenerationQueue.bind(generator);
      await processQueue();

      // Verify the task was processed
      expect(mockResolve).toHaveBeenCalled();
      expect(mockReject).not.toHaveBeenCalled();
      expect((generator as any)._generationQueue.length).toBe(0);
    });

    test('should exercise queue processing with task rejection', async () => {
      await generator.initialize();

      // Create a mock task with resolve/reject functions
      const mockResolve = jest.fn();
      const mockReject = jest.fn();

      // Mock the generate method to throw an error
      const originalGenerate = generator.generate;
      generator.generate = jest.fn().mockRejectedValue(new Error('Generation failed'));

      // Add a task to the queue
      (generator as any)._generationQueue.push({
        context: { id: 'error-context' },
        options: {},
        resolve: mockResolve,
        reject: mockReject
      });

      // Process the queue
      const processQueue = (generator as any)._processGenerationQueue.bind(generator);
      await processQueue();

      // Verify the task was rejected
      expect(mockResolve).not.toHaveBeenCalled();
      expect(mockReject).toHaveBeenCalledWith(expect.any(Error));

      // Restore original method
      generator.generate = originalGenerate;
    });

    test('should exercise all template generation methods with comprehensive contexts', async () => {
      await generator.initialize();

      // Test system documentation with comprehensive context
      const comprehensiveSystemContext = {
        id: 'comprehensive-system',
        name: 'Comprehensive System',
        version: '2.0.0',
        description: 'A comprehensive system with all features',
        components: [
          {
            id: 'comp-1',
            name: 'Component 1',
            type: 'service',
            version: '1.0.0',
            description: 'Primary service component',
            dependencies: ['comp-2'],
            configuration: { setting1: 'value1' }
          },
          {
            id: 'comp-2',
            name: 'Component 2',
            type: 'database',
            version: '1.1.0',
            description: 'Database component'
          }
        ],
        configuration: {
          id: 'config-comprehensive',
          parameters: {
            maxConnections: 100,
            timeout: 30000,
            retryAttempts: 3
          },
          environment: {
            NODE_ENV: 'production',
            LOG_LEVEL: 'info'
          },
          security: {
            encryption: true,
            authentication: 'oauth2',
            authorization: 'rbac'
          },
          performance: {
            caching: true,
            compression: true
          }
        },
        metadata: {
          created: '2024-01-01T00:00:00Z',
          modified: '2024-01-15T12:00:00Z',
          creator: 'system-architect',
          versionHistory: ['1.0.0', '1.5.0', '2.0.0'],
          tags: ['production', 'critical', 'high-availability']
        },
        dependencies: ['external-service-1', 'external-service-2'],
        securityLevel: 'high'
      };

      const result = await generator.generateSystemDocumentation(comprehensiveSystemContext, {
        format: 'markdown',
        includeTableOfContents: true,
        includeSections: {
          overview: true,
          rules: true,
          configuration: true,
          compliance: true,
          api: true,
          troubleshooting: true,
          appendices: true,
          glossary: true,
          references: true,
          changelog: true,
          migration: true,
          examples: true
        }
      });

      // Verify comprehensive content generation
      expect(result.content).toContain('Comprehensive System');
      expect(result.content).toContain('2.0.0');
      expect(result.content).toContain('2'); // Component count
      expect(result.metadata.sectionsCount).toBeGreaterThan(1);
      expect(result.metadata.rulesCount).toBeGreaterThanOrEqual(0); // Rules count may vary
    });

    test('should exercise error handling in batch processing', async () => {
      await generator.initialize();

      // Mock the generate method to fail for specific contexts
      const originalGenerate = generator.generate;
      generator.generate = jest.fn().mockImplementation((context) => {
        if (context.id === 'error-context') {
          throw new Error('Simulated generation error');
        }
        return originalGenerate.call(generator, context, {});
      });

      const contexts = [
        {
          id: 'success-context',
          name: 'Success Context',
          version: '1.0.0',
          components: [],
          configuration: {
            id: 'config-success',
            parameters: {},
            environment: {},
            security: {}
          },
          metadata: {
            created: '2024-01-01T00:00:00Z',
            modified: '2024-01-01T00:00:00Z',
            creator: 'test-team'
          }
        },
        {
          id: 'error-context',
          name: 'Error Context',
          version: '1.0.0'
        }
      ];

      // Should throw error due to failed context
      await expect(generator.generateBatchDocumentation(contexts, {})).rejects.toThrow('Simulated generation error');

      // Restore original method
      generator.generate = originalGenerate;
    });

    test('should exercise initialization error handling paths', async () => {
      const errorGenerator = new GovernanceSystemDocGenerator({
        generatorId: 'init-error-generator',
        generatorName: 'Init Error Generator',
        version: '1.0.0'
      });

      // Mock the doInitialize method to throw an error
      const originalDoInitialize = (errorGenerator as any).doInitialize;
      (errorGenerator as any).doInitialize = jest.fn().mockRejectedValue(new Error('Initialization failed'));

      // Should handle initialization error
      await expect(errorGenerator.initialize()).rejects.toThrow('Initialization failed');

      // Restore original method
      (errorGenerator as any).doInitialize = originalDoInitialize;
    });

    test('should exercise shutdown error handling paths', async () => {
      await generator.initialize();

      // Mock the doShutdown method to throw an error
      const originalDoShutdown = (generator as any).doShutdown;
      (generator as any).doShutdown = jest.fn().mockRejectedValue(new Error('Shutdown failed'));

      // Should handle shutdown error
      await expect(generator.shutdown()).rejects.toThrow('Shutdown failed');

      // Restore original method
      (generator as any).doShutdown = originalDoShutdown;
    });

    test('should exercise validation error handling with timing failures', async () => {
      await generator.initialize();

      // Mock the timing.start to throw an error
      (generator as any)._resilientTimer.start = jest.fn().mockImplementation(() => {
        throw new Error('Timing start failed');
      });

      // Should handle timing error gracefully and return error result
      const result = await generator.validate();
      expect(result.status).toBe('invalid');
      expect(result.errors).toContain('Timing start failed');

      // Restore original timer
      (generator as any)._resilientTimer.start = jest.fn().mockReturnValue({
        end: jest.fn().mockReturnValue({ duration: 100, success: true })
      });
    });

    test('should exercise output validation error handling with timing failures', async () => {
      await generator.initialize();

      const validOutput = {
        id: 'timing-error-output',
        context: 'test-context',
        format: 'markdown',
        content: 'Test content',
        metadata: {
          contextId: 'test-context',
          generatedAt: new Date().toISOString(),
          format: 'markdown',
          version: '1.0.0',
          authority: 'test',
          complianceLevel: 'standard',
          securityLevel: 'standard',
          rulesCount: 1,
          sectionsCount: 1,
          validationStatus: 'validated',
          auditTrail: []
        },
        generatedAt: new Date().toISOString(),
        version: '1.0.0',
        auditTrail: []
      };

      // Mock the timing.start to throw an error
      (generator as any)._resilientTimer.start = jest.fn().mockImplementation(() => {
        throw new Error('Output validation timing failed');
      });

      // Should handle timing error gracefully
      await expect(generator.validateOutput(validOutput)).rejects.toThrow('Output validation timing failed');

      // Restore original timer
      (generator as any)._resilientTimer.start = jest.fn().mockReturnValue({
        end: jest.fn().mockReturnValue({ duration: 100, success: true })
      });
    });

    test('should exercise comprehensive error scenarios in generation methods', async () => {
      await generator.initialize();

      // Test system documentation generation with timing error
      const systemContext = {
        id: 'timing-error-system',
        name: 'Timing Error System',
        version: '1.0.0',
        description: 'System for testing timing errors',
        components: [],
        configuration: {
          id: 'config-timing-error',
          parameters: {},
          environment: {},
          security: {}
        },
        metadata: {
          created: '2024-01-01T00:00:00Z',
          modified: '2024-01-01T00:00:00Z',
          creator: 'timing-error-team'
        }
      };

      // Mock the timing.start to throw an error
      (generator as any)._resilientTimer.start = jest.fn().mockImplementation(() => {
        throw new Error('System generation timing failed');
      });

      // Should handle timing error gracefully
      await expect(generator.generateSystemDocumentation(systemContext, {})).rejects.toThrow('System generation timing failed');

      // Restore original timer for other tests
      (generator as any)._resilientTimer.start = jest.fn().mockReturnValue({
        end: jest.fn().mockReturnValue({ duration: 100, success: true })
      });
    });

    test('should exercise all uncovered conditional branches', async () => {
      await generator.initialize();

      // Test with undefined/null values to trigger different branches
      const edgeCaseContext = {
        id: 'edge-case-system',
        name: undefined as any, // Test undefined name
        version: null as any, // Test null version
        description: '', // Test empty description
        components: null as any, // Test null components
        configuration: {
          id: 'config-edge-case',
          parameters: null as any,
          environment: undefined as any,
          security: {}
        },
        metadata: {
          created: '2024-01-01T00:00:00Z',
          modified: '2024-01-01T00:00:00Z',
          creator: null as any // Test null creator
        }
      };

      // Should handle edge cases gracefully
      const result = await generator.generateSystemDocumentation(edgeCaseContext, {});

      expect(result).toBeDefined();
      expect(result.content).toBeDefined();
      expect(result.content).toContain('null'); // Should handle null values
    });
  });
});
