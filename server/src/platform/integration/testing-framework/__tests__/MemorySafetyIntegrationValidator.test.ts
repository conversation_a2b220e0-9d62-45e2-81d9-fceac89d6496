/**
 * ============================================================================
 * AI CONTEXT: Memory Safety Integration Validator Test Suite
 * Purpose: Comprehensive test coverage for memory safety validation framework
 * Complexity: Complex - Enterprise memory safety validation testing
 * AI Navigation: 8 sections, comprehensive test coverage
 * Lines: Target 95%+ coverage / Critical limit 2200
 * ============================================================================
 */

/**
 * Memory Safety Integration Validator Test Suite
 * 
 * Comprehensive test suite for MemorySafetyIntegrationValidator implementing
 * surgical precision testing patterns from lessons learned to achieve 95%+ coverage.
 * 
 * Includes integration tests, error scenarios, edge cases, and performance validation
 * following Anti-Simplification Policy and MEM-SAFE-002 compliance requirements.
 */

// ============================================================================
// SECTION 1: IMPORTS & TEST SETUP
// AI Context: Test dependencies and configuration
// ============================================================================

import { jest, describe, beforeEach, afterEach, it, expect, beforeAll, afterAll } from '@jest/globals';
import { MemorySafetyIntegrationValidator } from '../MemorySafetyIntegrationValidator';
import {
  TMemorySafetyIntegrationValidatorConfig,
  TMemorySafetyTestSuite,
  TMemorySafetyTest,
  TMemoryLeakDetectionConfig,
  TResourceValidationConfig,
  TMEMSAFE002ComplianceConfig,
  TMemorySafetyTestConfig
} from '../../../../../../shared/src/types/platform/integration/memory-safety-testing-types';

// Mock BaseTrackingService for testing
jest.mock('../../../tracking/core-data/base/BaseTrackingService', () => {
  return {
    BaseTrackingService: class MockBaseTrackingService {
      protected _config: any;
      private _ready = false;
      private _intervals: Map<string, NodeJS.Timeout> = new Map();
      private _timeouts: Map<string, NodeJS.Timeout> = new Map();

      constructor(config: any) {
        this._config = config;
      }

      async initialize(): Promise<void> {
        this._ready = true;
        await this.doInitialize();
      }

      async shutdown(): Promise<void> {
        this._ready = false;
        // Clear all intervals and timeouts
        this._intervals.forEach(interval => clearInterval(interval));
        this._timeouts.forEach(timeout => clearTimeout(timeout));
        this._intervals.clear();
        this._timeouts.clear();
        await this.doShutdown();
      }

      isReady(): boolean {
        return this._ready;
      }

      generateId(): string {
        return `test-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
      }

      logInfo(message: string, data?: any): void {
        console.log(`[TEST-INFO] ${message}`, data);
      }

      logError(message: string, data?: any): void {
        console.error(`[TEST-ERROR] ${message}`, data);
      }

      logWarning(message: string, data?: any): void {
        console.warn(`[TEST-WARNING] ${message}`, data);
      }

      createSafeInterval(callback: () => void, interval: number, name: string): void {
        const intervalId = setInterval(callback, interval);
        this._intervals.set(name, intervalId);
      }

      createSafeTimeout(callback: () => void, timeout: number, name: string): void {
        const timeoutId = setTimeout(callback, timeout);
        this._timeouts.set(name, timeoutId);
      }

      protected async doInitialize(): Promise<void> {
        // Override in subclass
      }

      protected async doShutdown(): Promise<void> {
        // Override in subclass
      }

      protected getServiceName(): string {
        return 'MockMemorySafetyValidator';
      }

      protected getServiceVersion(): string {
        return '1.0.0';
      }

      protected async doTrack(_data: any): Promise<void> {
        // Mock tracking implementation
      }
    }
  };
});

// Mock ResilientTimer and ResilientMetricsCollector
jest.mock('../../../../../../shared/src/base/utils/ResilientTiming', () => ({
  ResilientTimer: jest.fn().mockImplementation(() => ({
    start: jest.fn().mockReturnValue({
      end: jest.fn().mockReturnValue(100)
    }),
    startContext: jest.fn().mockReturnValue({
      end: jest.fn().mockReturnValue(100)
    })
  }))
}));

jest.mock('../../../../../../shared/src/base/utils/ResilientMetrics', () => ({
  ResilientMetricsCollector: jest.fn().mockImplementation(() => ({
    collect: jest.fn().mockReturnValue({}),
    getMetrics: jest.fn().mockReturnValue({})
  }))
}));

jest.mock('../../../../../../shared/src/base/timer-coordination/modules/TimerConfiguration', () => ({
  createResilientTimer: jest.fn().mockReturnValue({
    start: jest.fn().mockReturnValue({
      end: jest.fn().mockReturnValue(100)
    })
  }),
  createResilientMetricsCollector: jest.fn().mockReturnValue({
    collect: jest.fn().mockReturnValue({}),
    getMetrics: jest.fn().mockReturnValue({})
  })
}));

// ============================================================================
// SECTION 2: TEST CONFIGURATION AND UTILITIES
// AI Context: Test data and helper functions
// ============================================================================

describe('MemorySafetyIntegrationValidator', () => {
  let validator: MemorySafetyIntegrationValidator;
  let mockConfig: TMemorySafetyIntegrationValidatorConfig;
  let mockTestSuite: TMemorySafetyTestSuite;

  // Test configuration factory
  const createMockConfig = (): TMemorySafetyIntegrationValidatorConfig => ({
    validatorId: 'test-validator-001',
    memorySafetyTestEnvironments: [{
      environmentId: 'test-env-001',
      environmentName: 'Test Environment',
      environmentType: 'memory-safety',
      systems: ['test-system'],
      memoryTools: ['heap-analyzer'],
      isolation: true,
      monitoring: true,
      resourceLimits: {
        maxMemory: '1GB',
        maxCpu: '2',
        maxDuration: 300000,
        maxConcurrency: 5,
        maxStorage: '10GB',
        maxNetworkBandwidth: '100Mbps',
        metadata: {}
      },
      metadata: {}
    }],
    complianceStandards: [{
      standardId: 'mem-safe-002',
      standardName: 'MEM-SAFE-002',
      version: '2.0',
      applicablePatterns: ['BaseTrackingService'],
      validationFrequency: 'hourly',
      complianceChecks: [{
        checkId: 'inheritance-check',
        checkName: 'Inheritance Compliance Check',
        checkType: 'inheritance',
        validationCriteria: ['base-tracking-service-inheritance'],
        severity: 'high',
        metadata: {}
      }],
      metadata: {}
    }],
    memorySafetyTestSuites: [{
      suiteId: 'test-suite-001',
      suiteName: 'Basic Memory Safety Test Suite',
      testCategories: ['leak-detection', 'compliance'],
      memorySafetyTests: [],
      executionSettings: {
        timeout: 300000,
        retryPolicy: {
          maxRetries: 1,
          retryDelay: 30000,
          backoffStrategy: 'linear',
          retryConditions: ['timeout'],
          metadata: {}
        },
        cleanupPolicy: 'always',
        parallelExecution: false,
        maxConcurrency: 1,
        metadata: {}
      },
      metadata: {}
    }],
    validationSettings: {
      enabledValidations: ['leak-detection', 'compliance', 'resource-validation'],
      validationFrequency: 'manual',
      alertThresholds: [],
      autoRemediation: false,
      reportingLevel: 'detailed',
      metadata: {}
    },
    monitoringSettings: {
      enabled: true,
      samplingInterval: 5000,
      retentionPeriod: 86400000,
      alerting: {
        enabled: true,
        alertChannels: ['console'],
        escalationPolicy: 'standard',
        suppressionRules: [],
        metadata: {}
      },
      dataCollection: {
        enabled: true,
        collectionScope: ['memory', 'performance'],
        samplingRate: 1.0,
        dataFormat: 'json',
        storageLocation: 'memory',
        metadata: {}
      },
      metadata: {}
    },
    reportingSettings: {
      enabled: true,
      reportFormats: ['json'],
      deliveryMethods: ['console'],
      schedules: [],
      recipients: [],
      metadata: {}
    },
    securitySettings: {
      encryptionEnabled: false,
      auditingEnabled: true,
      accessControl: 'basic',
      dataClassification: 'internal',
      complianceRequirements: ['MEM-SAFE-002'],
      metadata: {}
    },
    metadata: {}
  });

  // Test suite factory
  const createMockTestSuite = (): TMemorySafetyTestSuite => ({
    suiteId: 'test-suite-001',
    suiteName: 'Mock Memory Safety Test Suite',
    testCategories: ['leak-detection', 'compliance'],
    memorySafetyTests: [{
      testId: 'test-001',
      testName: 'Basic Memory Safety Test',
      testType: 'leak-detection',
      targetComponents: ['test-component'],
      testScenarios: [{
        scenarioId: 'scenario-001',
        description: 'Basic memory leak detection',
        testSteps: ['allocate-memory', 'perform-operations', 'check-leaks'],
        memoryConstraints: {
          maxHeapSize: 100 * 1024 * 1024,
          maxStackSize: 10 * 1024 * 1024,
          maxObjectCount: 10000,
          maxAllocationRate: 1000,
          gcPressureLimit: 80,
          metadata: {}
        },
        expectedBehavior: 'no-memory-leaks',
        validationCriteria: ['heap-stable', 'object-count-stable'],
        metadata: {}
      }],
      expectedResults: [{
        resultId: 'result-001',
        testId: 'test-001',
        expectedOutcome: 'pass',
        expectedMetrics: {
          maxMemoryUsage: 50 * 1024 * 1024,
          maxExecutionTime: 30000,
          expectedLeakCount: 0,
          expectedViolationCount: 0,
          performanceTargets: [],
          metadata: {}
        },
        validationCriteria: ['no-leaks', 'performance-within-limits'],
        metadata: {}
      }],
      complianceRequirements: ['MEM-SAFE-002'],
      metadata: {}
    }],
    executionSettings: {
      timeout: 300000,
      retryPolicy: {
        maxRetries: 1,
        retryDelay: 30000,
        backoffStrategy: 'linear',
        retryConditions: ['timeout'],
        metadata: {}
      },
      cleanupPolicy: 'always',
      parallelExecution: false,
      maxConcurrency: 1,
      metadata: {}
    },
    metadata: {}
  });

  // ============================================================================
  // SECTION 3: TEST LIFECYCLE MANAGEMENT
  // AI Context: Setup and teardown for comprehensive testing
  // ============================================================================

  beforeAll(() => {
    // Global test setup
    jest.clearAllMocks();
  });

  beforeEach(() => {
    // Reset mocks and create fresh instances
    jest.clearAllMocks();
    mockConfig = createMockConfig();
    mockTestSuite = createMockTestSuite();
    validator = new MemorySafetyIntegrationValidator();
  });

  afterEach(async () => {
    // Cleanup after each test
    if (validator && validator.isReady()) {
      await validator.shutdown();
    }
    jest.clearAllMocks();
  });

  afterAll(() => {
    // Global test cleanup
    jest.restoreAllMocks();
  });

  // ============================================================================
  // SECTION 4: CONSTRUCTOR AND INITIALIZATION TESTS
  // AI Context: Test validator creation and initialization patterns
  // ============================================================================

  describe('Constructor and Initialization', () => {
    it('should create validator instance with default configuration', () => {
      expect(validator).toBeInstanceOf(MemorySafetyIntegrationValidator);
      expect((validator as any).generateId()).toMatch(/^test-\d+-[a-z0-9]+$/);
    });

    it('should create validator instance with custom configuration', () => {
      const customConfig: any = {
        maxIntervals: 10,
        maxTimeouts: 15,
        maxCacheSize: 200 * 1024 * 1024,
        memoryThresholdMB: 500
      };

      const customValidator = new MemorySafetyIntegrationValidator(customConfig);
      expect(customValidator).toBeInstanceOf(MemorySafetyIntegrationValidator);
    });

    it('should initialize validator successfully', async () => {
      await validator.initialize();
      expect(validator.isReady()).toBe(true);
    });

    it('should handle initialization errors gracefully', async () => {
      // Mock initialization failure
      const doInitializeSpy = jest.spyOn(validator as any, 'doInitialize')
        .mockRejectedValueOnce(new Error('Initialization failed'));

      await expect(validator.initialize()).rejects.toThrow('Initialization failed');
      expect(doInitializeSpy).toHaveBeenCalled();
    });

    it('should shutdown validator successfully', async () => {
      await validator.initialize();
      expect(validator.isReady()).toBe(true);

      await validator.shutdown();
      expect(validator.isReady()).toBe(false);
    });

    it('should handle shutdown errors gracefully', async () => {
      await validator.initialize();

      // Mock internal shutdown operation failure, not the main doShutdown
      const stopValidationsSpy = jest.spyOn(validator as any, '_stopAllActiveValidations')
        .mockRejectedValueOnce(new Error('Shutdown failed'));

      // Should not throw, but log error
      await validator.shutdown();
      expect(stopValidationsSpy).toHaveBeenCalled();
    });
  });

  // ============================================================================
  // SECTION 5: MEMORY SAFETY VALIDATOR INTERFACE TESTS
  // AI Context: Test core validator management and validation methods
  // ============================================================================

  describe('Memory Safety Validator Interface', () => {
    beforeEach(async () => {
      await validator.initialize();
    });

    describe('initializeMemorySafetyValidator', () => {
      it('should initialize validator with valid configuration', async () => {
        const result = await validator.initializeMemorySafetyValidator(mockConfig);

        expect(result.success).toBe(true);
        expect(result.validatorId).toBe(mockConfig.validatorId);
        expect(result.initializedComponents).toContain('testing-infrastructure');
        expect(result.initializedComponents).toContain('monitoring-systems');
        expect(result.initializedComponents).toContain('compliance-validation');
        expect(result.initializedComponents).toContain('reporting-systems');
        expect(result.errors).toHaveLength(0);
      });

      it('should handle invalid configuration', async () => {
        const invalidConfig = { ...mockConfig, validatorId: '' };
        const result = await validator.initializeMemorySafetyValidator(invalidConfig);

        expect(result.success).toBe(false);
        expect(result.errors.length).toBeGreaterThan(0);
        expect(result.errors[0].message).toContain('Invalid configuration');
      });

      it('should handle initialization errors', async () => {
        // Mock internal method to throw error
        const reinitializeSpy = jest.spyOn(validator as any, '_reinitializeWithConfig')
          .mockRejectedValueOnce(new Error('Reinitialization failed'));

        const result = await validator.initializeMemorySafetyValidator(mockConfig);

        expect(result.success).toBe(false);
        expect(result.errors.length).toBeGreaterThan(0);
        expect(reinitializeSpy).toHaveBeenCalled();
      });
    });

    describe('startMemorySafetyValidation', () => {
      beforeEach(async () => {
        await validator.initializeMemorySafetyValidator(mockConfig);
      });

      it('should start validation successfully', async () => {
        const result = await validator.startMemorySafetyValidation();

        expect(result.success).toBe(true);
        expect(result.validationId).toBeDefined();
        expect(result.targetComponents.length).toBeGreaterThan(0);
        expect(result.estimatedDuration).toBeGreaterThan(0);
        expect(result.monitoringEnabled).toBe(true);
      });

      it('should prevent starting validation when not initialized', async () => {
        const uninitializedValidator = new MemorySafetyIntegrationValidator();
        // Do NOT call initialize() - we want a truly uninitialized validator

        // Try to start validation without any initialization
        const result = await uninitializedValidator.startMemorySafetyValidation();

        expect(result.success).toBe(false);
        expect(result.metadata.error).toContain('Validator not initialized');
      });

      it('should prevent starting validation when already active', async () => {
        // Start first validation
        const firstResult = await validator.startMemorySafetyValidation();
        expect(firstResult.success).toBe(true);

        // Try to start second validation
        const secondResult = await validator.startMemorySafetyValidation();
        expect(secondResult.success).toBe(false);
        expect(secondResult.metadata.error).toContain('already active');
      });
    });

    describe('stopMemorySafetyValidation', () => {
      beforeEach(async () => {
        await validator.initializeMemorySafetyValidator(mockConfig);
      });

      it('should stop validation successfully', async () => {
        // Start validation first
        const startResult = await validator.startMemorySafetyValidation();
        expect(startResult.success).toBe(true);

        // Stop validation
        const stopResult = await validator.stopMemorySafetyValidation();
        expect(stopResult.success).toBe(true);
        expect(stopResult.validationId).toBe(startResult.validationId);
        expect(stopResult.finalStatus).toBe('completed');
        expect(stopResult.resultsGenerated).toBe(true);
        expect(stopResult.cleanupCompleted).toBe(true);
      });

      it('should handle stopping when no validation is active', async () => {
        const result = await validator.stopMemorySafetyValidation();

        expect(result.success).toBe(false);
        expect(result.metadata.error).toContain('No active validation');
      });
    });

    describe('validateMemorySafety', () => {
      beforeEach(async () => {
        await validator.initializeMemorySafetyValidator(mockConfig);
      });

      it('should execute memory safety validation successfully', async () => {
        const result = await validator.validateMemorySafety(mockTestSuite);

        expect(result.success).toBe(true);
        expect(result.testId).toBe(mockTestSuite.suiteId);
        expect(result.overallStatus).toBe('passed');
        expect(result.testResults).toBeDefined();
        expect(result.memoryLeaksDetected).toBeDefined();
        expect(result.complianceScore).toBeGreaterThanOrEqual(0);
        expect(result.totalExecutionTime).toBeGreaterThanOrEqual(0);
      });

      it('should handle test execution errors', async () => {
        // Mock test execution to throw error
        const executeTestsSpy = jest.spyOn(validator as any, '_executeMemorySafetyTests')
          .mockRejectedValueOnce(new Error('Test execution failed'));

        const result = await validator.validateMemorySafety(mockTestSuite);

        expect(result.success).toBe(false);
        expect(result.overallStatus).toBe('failed');
        expect(result.errors.length).toBeGreaterThan(0);
        expect(executeTestsSpy).toHaveBeenCalled();
      });

      it('should calculate compliance score correctly', async () => {
        // Mock test results with violations
        const mockTestResults = [{
          success: false,
          testId: 'test-001',
          testName: 'Test with violations',
          executionTime: 1000,
          status: 'failed' as const,
          memoryMetrics: {} as any,
          leaksDetected: [],
          complianceViolations: [
            { violationId: 'v1', severity: 'high' },
            { violationId: 'v2', severity: 'medium' }
          ],
          performanceMetrics: {} as any,
          errors: [],
          metadata: {}
        }];

        jest.spyOn(validator as any, '_executeMemorySafetyTests')
          .mockResolvedValueOnce(mockTestResults);

        const result = await validator.validateMemorySafety(mockTestSuite);

        expect(result.complianceScore).toBeLessThan(100);
      });
    });

    describe('detectMemoryLeaks', () => {
      beforeEach(async () => {
        await validator.initializeMemorySafetyValidator(mockConfig);
      });

      it('should detect memory leaks successfully', async () => {
        const leakDetectionConfig: TMemoryLeakDetectionConfig = {
          detectionId: 'leak-detection-001',
          targetSystems: ['test-system'],
          monitoringDuration: 30000,
          leakThresholds: [{
            thresholdType: 'memory-growth',
            threshold: 10 * 1024 * 1024,
            timeWindow: 30000,
            severity: 'high',
            alertAction: 'alert',
            metadata: {}
          }],
          analysisDepth: 'detailed',
          reportingFormat: ['json'],
          metadata: {}
        };

        const result = await validator.detectMemoryLeaks(leakDetectionConfig);

        expect(result.success).toBe(true);
        expect(result.detectionId).toBe(leakDetectionConfig.detectionId);
        expect(result.monitoringDuration).toBe(leakDetectionConfig.monitoringDuration);
        expect(result.leaksDetected).toBeDefined();
        expect(result.memoryGrowthAnalysis).toBeDefined();
        expect(result.recommendations).toBeDefined();
      });

      it('should handle leak detection errors', async () => {
        const leakDetectionConfig: TMemoryLeakDetectionConfig = {
          detectionId: 'leak-detection-002',
          targetSystems: ['invalid-system'],
          monitoringDuration: 30000,
          leakThresholds: [],
          analysisDepth: 'basic',
          reportingFormat: ['json'],
          metadata: {}
        };

        // Mock leak detector initialization to fail
        jest.spyOn(validator as any, '_initializeLeakDetector')
          .mockRejectedValueOnce(new Error('Leak detector initialization failed'));

        const result = await validator.detectMemoryLeaks(leakDetectionConfig);

        expect(result.success).toBe(false);
        expect(result.errors.length).toBeGreaterThan(0);
      });
    });
  });

  // ============================================================================
  // SECTION 6: MEMORY SAFETY TESTER INTERFACE TESTS
  // AI Context: Test memory safety testing capabilities and test management
  // ============================================================================

  describe('Memory Safety Tester Interface', () => {
    beforeEach(async () => {
      await validator.initialize();
      await validator.initializeMemorySafetyValidator(mockConfig);
    });

    describe('initializeMemorySafetyTesting', () => {
      it('should initialize memory safety testing successfully', async () => {
        const testConfig: TMemorySafetyTestConfig = {
          testId: 'test-config-001',
          testName: 'Basic Memory Safety Test',
          testType: 'leak-detection',
          targetComponents: ['test-component'],
          testEnvironment: 'test-env',
          testParameters: {
            memoryLimits: {
              heapLimit: 100 * 1024 * 1024,
              stackLimit: 10 * 1024 * 1024,
              bufferLimit: 50 * 1024 * 1024,
              cacheLimit: 25 * 1024 * 1024,
              totalLimit: 200 * 1024 * 1024,
              metadata: {}
            },
            testDuration: 30000,
            samplingInterval: 1000,
            leakThresholds: [],
            performanceTargets: [],
            metadata: {}
          },
          executionSettings: {
            timeout: 60000,
            retryPolicy: {
              maxRetries: 1,
              retryDelay: 5000,
              backoffStrategy: 'linear',
              retryConditions: ['timeout'],
              metadata: {}
            },
            cleanupPolicy: 'always',
            parallelExecution: false,
            maxConcurrency: 1,
            metadata: {}
          },
          metadata: {}
        };

        const result = await validator.initializeMemorySafetyTesting(testConfig);

        expect(result.success).toBe(true);
        expect(result.testId).toBe(testConfig.testId);
        expect(result.initializedTests).toContain(testConfig.testId);
        expect(result.configuration).toEqual(testConfig);
        expect(result.errors).toHaveLength(0);
      });

      it('should handle invalid test configuration', async () => {
        const invalidTestConfig = {
          testId: '', // Invalid: empty test ID
          testName: 'Invalid Test',
          testType: 'leak-detection' as const,
          targetComponents: [],
          testEnvironment: 'test-env',
          testParameters: {} as any,
          executionSettings: {} as any,
          metadata: {}
        };

        const result = await validator.initializeMemorySafetyTesting(invalidTestConfig);

        expect(result.success).toBe(false);
        expect(result.errors.length).toBeGreaterThan(0);
        expect(result.errors[0].message).toContain('Invalid test configuration');
      });
    });

    describe('enableMemorySafetyTestType and disableMemorySafetyTestType', () => {
      it('should enable memory safety test type', async () => {
        const testType = 'performance-testing';

        await validator.enableMemorySafetyTestType(testType);

        // Verify test type is enabled (check internal state)
        const validatorConfig = (validator as any)._validatorConfig;
        expect(validatorConfig.validationSettings.enabledValidations).toContain(testType);
      });

      it('should disable memory safety test type', async () => {
        const testType = 'leak-detection';

        // First ensure it's enabled
        await validator.enableMemorySafetyTestType(testType);

        // Then disable it
        await validator.disableMemorySafetyTestType(testType);

        // Verify test type is disabled
        const validatorConfig = (validator as any)._validatorConfig;
        expect(validatorConfig.validationSettings.enabledValidations).not.toContain(testType);
      });

      it('should handle enabling already enabled test type', async () => {
        const testType = 'compliance';

        // Enable twice
        await validator.enableMemorySafetyTestType(testType);
        await validator.enableMemorySafetyTestType(testType);

        // Should only appear once
        const validatorConfig = (validator as any)._validatorConfig;
        const occurrences = validatorConfig.validationSettings.enabledValidations.filter((t: string) => t === testType).length;
        expect(occurrences).toBe(1);
      });

      it('should handle disabling non-enabled test type', async () => {
        const testType = 'non-existent-test';

        // Try to disable non-enabled test type (should not throw)
        await expect(validator.disableMemorySafetyTestType(testType)).resolves.not.toThrow();
      });
    });

    describe('executeMemorySafetyTest', () => {
      it('should execute memory safety test successfully', async () => {
        const memorySafetyTest = mockTestSuite.memorySafetyTests[0];

        const result = await validator.executeMemorySafetyTest(memorySafetyTest);

        expect(result.success).toBe(true);
        expect(result.testId).toBe(memorySafetyTest.testId);
        expect(result.testName).toBe(memorySafetyTest.testName);
        expect(result.status).toBe('passed');
        expect(result.executionTime).toBeGreaterThanOrEqual(0);
        expect(result.memoryMetrics).toBeDefined();
        expect(result.leaksDetected).toBeDefined();
        expect(result.complianceViolations).toBeDefined();
        expect(result.performanceMetrics).toBeDefined();
      });

      it('should handle test execution errors', async () => {
        const memorySafetyTest = mockTestSuite.memorySafetyTests[0];

        // Temporarily disable retries by modifying the validator config
        const originalConfig = (validator as any)._validatorConfig;
        if (originalConfig?.memorySafetyTestSuites?.[0]?.executionSettings?.retryPolicy) {
          originalConfig.memorySafetyTestSuites[0].executionSettings.retryPolicy.maxRetries = 0;
          originalConfig.memorySafetyTestSuites[0].executionSettings.retryPolicy.retryDelay = 0;
        }

        // Mock the internal test execution method to fail immediately
        jest.spyOn(validator as any, '_executeMemorySafetyTestInternal')
          .mockRejectedValueOnce(new Error('Test scenario execution failed'));

        const result = await validator.executeMemorySafetyTest(memorySafetyTest);

        expect(result.success).toBe(false);
        expect(result.status).toBe('failed');
        expect(result.errors.length).toBeGreaterThan(0);

        // Restore original config
        if (originalConfig?.memorySafetyTestSuites?.[0]?.executionSettings?.retryPolicy) {
          originalConfig.memorySafetyTestSuites[0].executionSettings.retryPolicy.maxRetries = 1;
          originalConfig.memorySafetyTestSuites[0].executionSettings.retryPolicy.retryDelay = 30000;
        }
      }, 5000); // Reduced timeout since we disabled retries

      it('should add test result to history', async () => {
        const memorySafetyTest = mockTestSuite.memorySafetyTests[0];
        const initialHistoryLength = (validator as any)._testHistory.length;

        await validator.executeMemorySafetyTest(memorySafetyTest);

        const finalHistoryLength = (validator as any)._testHistory.length;
        expect(finalHistoryLength).toBe(initialHistoryLength + 1);
      });
    });

    describe('runConcurrentMemorySafetyTests', () => {
      it('should run concurrent memory safety tests successfully', async () => {
        const memorySafetyTests = [
          mockTestSuite.memorySafetyTests[0],
          {
            ...mockTestSuite.memorySafetyTests[0],
            testId: 'test-002',
            testName: 'Concurrent Test 2'
          }
        ];

        const result = await validator.runConcurrentMemorySafetyTests(memorySafetyTests);

        expect(result.success).toBe(true);
        expect(result.totalTests).toBe(memorySafetyTests.length);
        expect(result.passedTests).toBe(memorySafetyTests.length);
        expect(result.failedTests).toBe(0);
        expect(result.testResults).toHaveLength(memorySafetyTests.length);
        expect(result.overallExecutionTime).toBeGreaterThanOrEqual(0);
        expect(result.resourceContention).toBeDefined();
      });

      it('should handle concurrent test execution errors', async () => {
        const memorySafetyTests = [mockTestSuite.memorySafetyTests[0]];

        // Mock executeMemorySafetyTest to fail
        jest.spyOn(validator, 'executeMemorySafetyTest')
          .mockRejectedValueOnce(new Error('Concurrent test execution failed'));

        const result = await validator.runConcurrentMemorySafetyTests(memorySafetyTests);

        expect(result.success).toBe(false);
        expect(result.errors.length).toBeGreaterThan(0);
      });

      it('should respect concurrency limits', async () => {
        const memorySafetyTests = Array.from({ length: 10 }, (_, i) => ({
          ...mockTestSuite.memorySafetyTests[0],
          testId: `test-${i + 1}`,
          testName: `Concurrent Test ${i + 1}`
        }));

        // Mock test engine with low concurrency limit
        (validator as any)._testEngine = {
          maxConcurrentTests: 2,
          initialized: true
        };

        const result = await validator.runConcurrentMemorySafetyTests(memorySafetyTests);

        expect(result.success).toBe(true);
        expect(result.totalTests).toBe(10);
        expect(result.metadata.maxConcurrency).toBe(2);
      });
    });

    describe('getMemorySafetyTestHistory', () => {
      it('should retrieve memory safety test history successfully', async () => {
        // Execute some tests to create history
        const memorySafetyTest = mockTestSuite.memorySafetyTests[0];
        await validator.executeMemorySafetyTest(memorySafetyTest);
        await validator.executeMemorySafetyTest({
          ...memorySafetyTest,
          testId: 'test-002',
          testName: 'History Test 2'
        });

        const result = await validator.getMemorySafetyTestHistory();

        expect(result.totalTests).toBeGreaterThanOrEqual(2);
        expect(result.testResults).toHaveLength(result.totalTests);
        expect(result.timeRange).toBeDefined();
        expect(result.trends).toBeDefined();
        expect(result.statistics).toBeDefined();
        expect(result.statistics.totalExecutions).toBe(result.totalTests);
      });

      it('should handle empty test history', async () => {
        const result = await validator.getMemorySafetyTestHistory();

        expect(result.totalTests).toBe(0);
        expect(result.testResults).toHaveLength(0);
        expect(result.statistics.totalExecutions).toBe(0);
        expect(result.statistics.successRate).toBe(0);
      });

      it('should handle test history retrieval errors', async () => {
        // Mock internal method to throw error
        jest.spyOn(validator as any, '_analyzeTestTrends')
          .mockRejectedValueOnce(new Error('Trend analysis failed'));

        const result = await validator.getMemorySafetyTestHistory();

        expect(result.metadata.error).toBeDefined();
      });
    });

    describe('clearMemorySafetyTestHistory', () => {
      beforeEach(async () => {
        // Create some test history
        const memorySafetyTest = mockTestSuite.memorySafetyTests[0];
        for (let i = 0; i < 5; i++) {
          await validator.executeMemorySafetyTest({
            ...memorySafetyTest,
            testId: `history-test-${i}`,
            testName: `History Test ${i}`
          });
        }
      });

      it('should clear test history by date criteria', async () => {
        const criteria = {
          olderThan: new Date(Date.now() + 1000), // Future date to clear all
          testTypes: [],
          status: [],
          maxRecords: 1000,
          metadata: {}
        };

        const initialHistory = await validator.getMemorySafetyTestHistory();
        expect(initialHistory.totalTests).toBeGreaterThan(0);

        await validator.clearMemorySafetyTestHistory(criteria);

        const finalHistory = await validator.getMemorySafetyTestHistory();
        expect(finalHistory.totalTests).toBe(0);
      });

      it('should clear test history by max records criteria', async () => {
        const criteria = {
          olderThan: new Date(0), // Very old date to keep all by date
          testTypes: [],
          status: [],
          maxRecords: 2, // Keep only 2 most recent
          metadata: {}
        };

        await validator.clearMemorySafetyTestHistory(criteria);

        const finalHistory = await validator.getMemorySafetyTestHistory();
        expect(finalHistory.totalTests).toBe(2);
      });

      it('should handle clear history errors gracefully', async () => {
        const criteria = {
          olderThan: new Date(),
          testTypes: [],
          status: [],
          maxRecords: 100,
          metadata: {}
        };

        // Should not throw even if there are issues
        await expect(validator.clearMemorySafetyTestHistory(criteria)).resolves.not.toThrow();
      });
    });
  });

  // ============================================================================
  // SECTION 7: PERFORMANCE AND HEALTH MONITORING TESTS
  // AI Context: Test performance metrics and health status monitoring
  // ============================================================================

  describe('Performance and Health Monitoring', () => {
    beforeEach(async () => {
      await validator.initialize();
      await validator.initializeMemorySafetyValidator(mockConfig);
    });

    describe('getMemorySafetyTestPerformance', () => {
      it('should retrieve performance metrics successfully', async () => {
        // Execute some tests to generate performance data
        const memorySafetyTest = mockTestSuite.memorySafetyTests[0];
        await validator.executeMemorySafetyTest(memorySafetyTest);

        const result = await validator.getMemorySafetyTestPerformance();

        expect(result.metricsId).toBeDefined();
        expect(result.timestamp).toBeInstanceOf(Date);
        expect(result.averageExecutionTime).toBeGreaterThanOrEqual(0);
        expect(result.throughput).toBeGreaterThanOrEqual(0);
        expect(result.successRate).toBeGreaterThanOrEqual(0);
        expect(result.successRate).toBeLessThanOrEqual(100);
        expect(result.errorRate).toBeGreaterThanOrEqual(0);
        expect(result.errorRate).toBeLessThanOrEqual(100);
        expect(result.resourceUtilization).toBeDefined();
        expect(result.bottlenecks).toBeDefined();
        expect(result.performanceTrend).toBeDefined();
      });

      it('should handle performance metrics retrieval errors', async () => {
        // Mock internal method to throw error
        jest.spyOn(validator as any, '_identifyPerformanceBottlenecks')
          .mockImplementationOnce(() => {
            throw new Error('Performance analysis failed');
          });

        const result = await validator.getMemorySafetyTestPerformance();

        expect(result.metadata.error).toBeDefined();
        expect(result.successRate).toBe(0);
        expect(result.errorRate).toBe(100);
      });

      it('should calculate performance metrics correctly with test history', async () => {
        // Execute multiple tests with different outcomes
        // const memorySafetyTest = mockTestSuite.memorySafetyTests[0]; // Not used in this test

        // Mock some test results
        const mockResults = [
          { status: 'passed', executionTime: 1000 },
          { status: 'passed', executionTime: 1500 },
          { status: 'failed', executionTime: 2000 },
          { status: 'passed', executionTime: 1200 }
        ];

        (validator as any)._testHistory = mockResults.map((result, index) => ({
          testId: `test-${index}`,
          testName: `Test ${index}`,
          status: result.status,
          executionTime: result.executionTime,
          leaksDetected: [],
          complianceViolations: [],
          performanceMetrics: {},
          errors: [],
          metadata: { executionTime: result.executionTime }
        }));

        const result = await validator.getMemorySafetyTestPerformance();

        expect(result.successRate).toBe(75); // 3 out of 4 passed
        expect(result.errorRate).toBe(25);   // 1 out of 4 failed
        expect(result.averageExecutionTime).toBe(1425); // Average of execution times
      });
    });

    describe('getMemorySafetyTestHealth', () => {
      it('should retrieve health status successfully', async () => {
        const result = await validator.getMemorySafetyTestHealth();

        expect(result.statusId).toBeDefined();
        expect(result.timestamp).toBeInstanceOf(Date);
        expect(result.overallHealth).toMatch(/^(healthy|warning|error|critical)$/);
        expect(result.testEngineHealth).toMatch(/^(healthy|warning|error|critical)$/);
        expect(result.validatorHealth).toMatch(/^(healthy|warning|error|critical)$/);
        expect(result.monitoringHealth).toMatch(/^(healthy|warning|error|critical)$/);
        expect(result.activeIssues).toBeDefined();
        expect(result.lastHealthCheck).toBeInstanceOf(Date);
      });

      it('should identify health issues correctly', async () => {
        // Create an uninitialized validator to trigger health issues
        const uninitializedValidator = new MemorySafetyIntegrationValidator();
        // Do NOT call initialize() - we want a truly uninitialized validator

        const result = await uninitializedValidator.getMemorySafetyTestHealth();

        expect(result.overallHealth).toBe('critical');
        expect(result.activeIssues.length).toBeGreaterThan(0);
        expect(result.activeIssues[0].description).toContain('not properly initialized');
      });

      it('should handle health status retrieval errors', async () => {
        // Mock internal method to throw error
        jest.spyOn(validator as any, '_determineTestEngineHealth')
          .mockImplementationOnce(() => {
            throw new Error('Health determination failed');
          });

        const result = await validator.getMemorySafetyTestHealth();

        expect(result.overallHealth).toBe('critical');
        expect(result.activeIssues.length).toBeGreaterThan(0);
        expect(result.metadata.error).toBeDefined();
      });
    });

    describe('getMemorySafetyMetrics', () => {
      it('should retrieve validator metrics successfully', async () => {
        const result = await validator.getMemorySafetyMetrics();

        expect(result.metricsId).toBeDefined();
        expect(result.timestamp).toBeInstanceOf(Date);
        expect(result.validationsPerformed).toBeGreaterThanOrEqual(0);
        expect(result.validationSuccessRate).toBeGreaterThanOrEqual(0);
        expect(result.averageValidationTime).toBeGreaterThanOrEqual(0);
        expect(result.memoryLeaksDetected).toBeGreaterThanOrEqual(0);
        expect(result.complianceViolations).toBeGreaterThanOrEqual(0);
        expect(result.systemHealth).toBeDefined();
        expect(result.performanceMetrics).toBeDefined();
        expect(result.resourceUtilization).toBeDefined();
      });

      it('should update system health based on validator status', async () => {
        // Set validator to error state
        (validator as any)._validatorData.validatorStatus = 'error';

        const result = await validator.getMemorySafetyMetrics();

        expect(result.systemHealth.overallHealth).toBe('error');
      });

      it('should handle metrics retrieval errors gracefully', async () => {
        // Mock internal state to cause issues
        (validator as any)._validationMetrics = null;

        const result = await validator.getMemorySafetyMetrics();

        // Should still return some metrics even with errors
        expect(result).toBeDefined();
      });
    });

    describe('getMemorySafetyStatus', () => {
      it('should retrieve validator status successfully', async () => {
        const result = await validator.getMemorySafetyStatus();

        expect(result.statusId).toBeDefined();
        expect(result.timestamp).toBeInstanceOf(Date);
        expect(result.validatorStatus).toMatch(/^(healthy|warning|error|offline)$/);
        expect(result.activeValidations).toBeGreaterThanOrEqual(0);
        expect(result.queuedValidations).toBeGreaterThanOrEqual(0);
        expect(result.systemLoad).toBeGreaterThanOrEqual(0);
        expect(result.systemLoad).toBeLessThanOrEqual(100);
        expect(result.memoryUsage).toBeGreaterThanOrEqual(0);
        expect(result.memoryUsage).toBeLessThanOrEqual(100);
        expect(result.lastValidation).toBeInstanceOf(Date);
        expect(result.nextScheduledValidation).toBeInstanceOf(Date);
        expect(result.alerts).toBeDefined();
      });

      it('should calculate system load correctly', async () => {
        // Add some active validations and monitoring sessions
        (validator as any)._activeValidations.set('val1', { status: 'running' });
        (validator as any)._activeValidations.set('val2', { status: 'running' });
        (validator as any)._monitoringSessions.set('mon1', { status: 'active' });

        const result = await validator.getMemorySafetyStatus();

        expect(result.activeValidations).toBe(2);
        expect(result.systemLoad).toBeGreaterThan(0);
      });
    });
  });

  // ============================================================================
  // SECTION 8: EDGE CASES AND ERROR SCENARIOS
  // AI Context: Test edge cases, error handling, and resilience patterns
  // ============================================================================

  describe('Edge Cases and Error Scenarios', () => {
    beforeEach(async () => {
      await validator.initialize();
    });

    describe('Resource Management Edge Cases', () => {
      it('should handle validateResourceManagement with empty target resources', async () => {
        await validator.initializeMemorySafetyValidator(mockConfig);

        const resourceValidationConfig: TResourceValidationConfig = {
          validationId: 'resource-validation-001',
          targetResources: [], // Empty array
          validationScope: ['memory', 'cpu'],
          resourceConstraints: {
            maxResourceCount: 100,
            maxResourceSize: 1024 * 1024,
            maxLifetime: 60000,
            cleanupRequirements: ['automatic'],
            metadata: {}
          },
          validationCriteria: ['resource-limits', 'cleanup-compliance'],
          metadata: {}
        };

        const result = await validator.validateResourceManagement(resourceValidationConfig);

        expect(result.success).toBe(true);
        expect(result.validatedResources).toHaveLength(0);
        expect(result.violations).toHaveLength(0);
        expect(result.complianceScore).toBe(100);
      });

      it('should handle validateMEMSAFE002Compliance with no compliance checks', async () => {
        await validator.initializeMemorySafetyValidator(mockConfig);

        const complianceConfig: TMEMSAFE002ComplianceConfig = {
          complianceId: 'compliance-001',
          targetComponents: ['test-component'],
          complianceChecks: [], // Empty array
          inheritanceValidation: {
            enabled: true,
            validateHierarchy: true,
            validateMethodImplementation: true,
            validatePropertyVisibility: true,
            requiredBaseClasses: ['BaseTrackingService'],
            metadata: {}
          },
          patternValidation: {
            enabled: true,
            validateMemorySafePatterns: true,
            validateResourceManagement: true,
            validateTimerManagement: true,
            requiredPatterns: ['memory-safe-inheritance'],
            metadata: {}
          },
          reportingRequirements: [],
          metadata: {}
        };

        const result = await validator.validateMEMSAFE002Compliance(complianceConfig);

        expect(result.success).toBe(true);
        expect(result.complianceChecks).toHaveLength(0);
        expect(result.overallScore).toBe(0);
      });
    });

    describe('Concurrent Operations Edge Cases', () => {
      it('should handle multiple concurrent validation starts', async () => {
        await validator.initializeMemorySafetyValidator(mockConfig);

        // Start multiple validations concurrently
        const promises = Array.from({ length: 5 }, () =>
          validator.startMemorySafetyValidation()
        );

        const results = await Promise.all(promises);

        // Only first should succeed, others should fail
        expect(results[0].success).toBe(true);
        expect(results.slice(1).every(r => !r.success)).toBe(true);
      });

      it('should handle concurrent test executions with resource contention', async () => {
        await validator.initializeMemorySafetyValidator(mockConfig);

        const memorySafetyTests = Array.from({ length: 20 }, (_, i) => ({
          ...mockTestSuite.memorySafetyTests[0],
          testId: `concurrent-test-${i}`,
          testName: `Concurrent Test ${i}`
        }));

        const result = await validator.runConcurrentMemorySafetyTests(memorySafetyTests);

        expect(result.success).toBe(true);
        expect(result.totalTests).toBe(20);
        expect(result.resourceContention).toBeDefined();
      });
    });

    describe('Memory Pressure Scenarios', () => {
      it('should handle memory pressure during validation', async () => {
        await validator.initializeMemorySafetyValidator(mockConfig);

        // Simulate memory pressure by creating large test history
        const largeTestHistory = Array.from({ length: 1000 }, (_, i) => ({
          testId: `memory-test-${i}`,
          testName: `Memory Test ${i}`,
          status: 'passed' as const,
          executionTime: 1000,
          leaksDetected: [],
          complianceViolations: [],
          performanceMetrics: {},
          errors: [],
          metadata: { executionTime: 1000 }
        }));

        (validator as any)._testHistory = largeTestHistory;

        const result = await validator.validateMemorySafety(mockTestSuite);

        expect(result.success).toBe(true);
        // Should still function under memory pressure
      });

      it('should handle monitoring data overflow', async () => {
        await validator.initializeMemorySafetyValidator(mockConfig);

        // Simulate monitoring data overflow
        const resourceMonitor = (validator as any)._resourceMonitor;
        if (resourceMonitor) {
          // Add many data points to trigger overflow handling
          resourceMonitor.dataPoints = Array.from({ length: 2000 }, (_, i) => ({
            timestamp: new Date(Date.now() - i * 1000),
            heapUsed: 100 * 1024 * 1024,
            heapTotal: 200 * 1024 * 1024,
            external: 10 * 1024 * 1024,
            rss: 150 * 1024 * 1024,
            activeValidations: 1,
            monitoringSessions: 1
          }));

          // Trigger monitoring data collection
          (validator as any)._collectMonitoringData();

          // Should limit data points to 1000
          expect(resourceMonitor.dataPoints.length).toBeLessThanOrEqual(1000);
        }
      });
    });

    describe('Error Recovery Scenarios', () => {
      it('should recover from validation errors and continue operations', async () => {
        await validator.initializeMemorySafetyValidator(mockConfig);

        // Start validation
        const startResult = await validator.startMemorySafetyValidation();
        expect(startResult.success).toBe(true);

        // Simulate validation error by corrupting internal state
        (validator as any)._validationActive = false;

        // Try to stop validation (should handle gracefully)
        const stopResult = await validator.stopMemorySafetyValidation();
        expect(stopResult.success).toBe(false);

        // Should be able to start new validation after error
        const newStartResult = await validator.startMemorySafetyValidation();
        expect(newStartResult.success).toBe(true);
      });

      it('should handle diagnostics errors gracefully', async () => {
        await validator.initializeMemorySafetyValidator(mockConfig);

        // Mock system diagnostics to fail
        jest.spyOn(validator as any, '_performSystemDiagnostics')
          .mockRejectedValueOnce(new Error('System diagnostics failed'));

        const result = await validator.performMemorySafetyDiagnostics();

        expect(result.overallHealth).toBe('critical');
        expect(result.errors.length).toBeGreaterThan(0);
        expect(result.errors[0].message).toContain('System diagnostics failed');
      });
    });

    describe('Configuration Edge Cases', () => {
      it('should handle malformed configuration gracefully', async () => {
        const malformedConfig = {
          validatorId: null, // Invalid type
          memorySafetyTestEnvironments: 'invalid', // Invalid type
          complianceStandards: undefined, // Missing required field
          // Missing other required fields
        } as any;

        const result = await validator.initializeMemorySafetyValidator(malformedConfig);

        expect(result.success).toBe(false);
        expect(result.errors.length).toBeGreaterThan(0);
      });

      it('should handle extremely large configurations', async () => {
        const largeConfig = {
          ...mockConfig,
          memorySafetyTestEnvironments: Array.from({ length: 1000 }, (_, i) => ({
            ...mockConfig.memorySafetyTestEnvironments[0],
            environmentId: `env-${i}`,
            environmentName: `Environment ${i}`
          }))
        };

        const result = await validator.initializeMemorySafetyValidator(largeConfig);

        expect(result.success).toBe(true);
        expect(result.configuration.memorySafetyTestEnvironments).toHaveLength(1000);
      });
    });
  });

  // ============================================================================
  // FINAL INTEGRATION AND PERFORMANCE TESTS
  // AI Context: End-to-end integration tests and performance validation
  // ============================================================================

  describe('Integration and Performance Tests', () => {
    beforeEach(async () => {
      await validator.initialize();
      await validator.initializeMemorySafetyValidator(mockConfig);
    });

    it('should complete full validation workflow successfully', async () => {
      // Start validation
      const startResult = await validator.startMemorySafetyValidation();
      expect(startResult.success).toBe(true);

      // Execute memory safety validation
      const validationResult = await validator.validateMemorySafety(mockTestSuite);
      expect(validationResult.success).toBe(true);

      // Detect memory leaks
      const leakDetectionConfig: TMemoryLeakDetectionConfig = {
        detectionId: 'integration-leak-detection',
        targetSystems: ['test-system'],
        monitoringDuration: 10000,
        leakThresholds: [],
        analysisDepth: 'basic',
        reportingFormat: ['json'],
        metadata: {}
      };

      const leakResult = await validator.detectMemoryLeaks(leakDetectionConfig);
      expect(leakResult.success).toBe(true);

      // Get metrics and status
      const metrics = await validator.getMemorySafetyMetrics();
      const status = await validator.getMemorySafetyStatus();
      const performance = await validator.getMemorySafetyTestPerformance();
      const health = await validator.getMemorySafetyTestHealth();

      expect(metrics).toBeDefined();
      expect(status).toBeDefined();
      expect(performance).toBeDefined();
      expect(health).toBeDefined();

      // Stop validation
      const stopResult = await validator.stopMemorySafetyValidation();
      expect(stopResult.success).toBe(true);
    });

    it('should maintain performance under load', async () => {
      const startTime = Date.now();

      // Execute multiple operations concurrently
      const operations = [
        validator.validateMemorySafety(mockTestSuite),
        validator.getMemorySafetyMetrics(),
        validator.getMemorySafetyStatus(),
        validator.getMemorySafetyTestPerformance(),
        validator.getMemorySafetyTestHealth()
      ];

      const results = await Promise.all(operations);
      const endTime = Date.now();
      const totalTime = endTime - startTime;

      // All operations should succeed
      expect(results.every(result => (result as any).success !== false)).toBe(true);

      // Should complete within reasonable time (10 seconds)
      expect(totalTime).toBeLessThan(10000);
    });

    it('should handle graceful shutdown with active operations', async () => {
      // Start validation
      await validator.startMemorySafetyValidation();

      // Start some operations
      const validationPromise = validator.validateMemorySafety(mockTestSuite);

      // Shutdown while operations are running
      await validator.shutdown();

      // Operations should complete or handle shutdown gracefully
      await expect(validationPromise).resolves.toBeDefined();
      expect(validator.isReady()).toBe(false);
    });
  });

  // ============================================================================
  // SECTION 8: ADVANCED COVERAGE TESTS
  // ============================================================================

  describe('Advanced Coverage Tests', () => {
    beforeEach(async () => {
      await validator.initialize();
      await validator.initializeMemorySafetyValidator(mockConfig);
    });

    afterEach(async () => {
      await validator.shutdown();
    });

    it('should execute periodic validation when configured', async () => {
      // Test the _performPeriodicValidation method directly
      const periodicValidationMethod = (validator as any)._performPeriodicValidation.bind(validator);

      // Should execute without errors
      await expect(periodicValidationMethod()).resolves.not.toThrow();
    });

    it('should handle test history filtering by test types', async () => {
      // Add some test history first
      const testResult = {
        testId: 'test-filter-1',
        testName: 'FilterTest',
        status: 'passed',
        metadata: { executionTime: Date.now() }
      };
      (validator as any)._testHistory.push(testResult);

      // Test filtering by test types
      const criteria = {
        olderThan: new Date(Date.now() - 1000), // 1 second ago
        testTypes: ['FilterTest'],
        status: [],
        maxRecords: 100,
        metadata: {}
      };

      await validator.clearMemorySafetyTestHistory(criteria);

      // The test should be removed from history
      const history = await validator.getMemorySafetyTestHistory();
      expect(history.testResults.length).toBe(0);
    });

    it('should handle test history filtering by status', async () => {
      // Add some test history first
      const testResult = {
        testId: 'test-status-1',
        testName: 'StatusTest',
        status: 'failed',
        metadata: { executionTime: Date.now() }
      };
      (validator as any)._testHistory.push(testResult);

      // Test filtering by status
      const criteria = {
        olderThan: new Date(Date.now() - 1000), // 1 second ago
        testTypes: [],
        status: ['failed'],
        maxRecords: 100,
        metadata: {}
      };

      await validator.clearMemorySafetyTestHistory(criteria);

      // The test should be removed from history
      const history = await validator.getMemorySafetyTestHistory();
      expect(history.testResults.length).toBe(0);
    });

    it('should handle test history clear errors gracefully', async () => {
      // Mock the test history to cause an error during filtering
      const originalHistory = (validator as any)._testHistory;

      // Create a problematic test entry that might cause errors
      (validator as any)._testHistory = [
        {
          testId: 'error-test',
          testName: 'ErrorTest',
          status: 'passed',
          metadata: { executionTime: 'invalid-date' } // Invalid date format
        }
      ];

      // Should handle errors gracefully
      const errorCriteria = {
        olderThan: new Date(),
        testTypes: [],
        status: [],
        maxRecords: 100,
        metadata: {}
      };
      await expect(validator.clearMemorySafetyTestHistory(errorCriteria)).resolves.not.toThrow();

      // Restore original history
      (validator as any)._testHistory = originalHistory;
    });

    it('should test private helper methods for coverage', async () => {
      // Test _processMemorySafetyIntegrationData
      const processMethod = (validator as any)._processMemorySafetyIntegrationData.bind(validator);
      const result = await processMethod({ test: 'data' });
      expect(result).toHaveProperty('processedId');
      expect(result).toHaveProperty('originalData');

      // Test _updateValidatorStateFromIntegration
      const updateMethod = (validator as any)._updateValidatorStateFromIntegration.bind(validator);
      await expect(updateMethod({})).resolves.not.toThrow();

      // Test _monitorMemorySafetyOperations
      const monitorMethod = (validator as any)._monitorMemorySafetyOperations.bind(validator);
      const monitorResult = await monitorMethod();
      expect(monitorResult).toHaveProperty('operationId');
      expect(monitorResult.status).toBe('healthy');

      // Test _checkSystemHealth
      const healthMethod = (validator as any)._checkSystemHealth.bind(validator);
      const healthResult = await healthMethod();
      expect(healthResult).toHaveProperty('healthId');
      expect(healthResult.overallHealth).toBe('healthy');
    });
  });
});
