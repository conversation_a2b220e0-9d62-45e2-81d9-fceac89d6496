/**
 * ============================================================================
 * LINE COVERAGE TESTS - MemorySafetyIntegrationValidator
 * ============================================================================
 * 
 * @fileoverview Surgical precision tests ensuring every executable line is executed
 * @version 1.0.0
 * @since 2025-09-06
 * 
 * Coverage Target: 95%+ Line Coverage
 * Focus Areas: Every executable line, unreachable code paths, complex logic flows
 * Testing Strategy: Exception handling, cleanup code paths, initialization sequences
 * 
 * Testing Techniques:
 * - Line-by-line execution verification
 * - Exception path testing for cleanup code
 * - Initialization and teardown sequence coverage
 * - Complex logic flow navigation
 */

import { MemorySafetyIntegrationValidator } from '../MemorySafetyIntegrationValidator';
import {
  TMemorySafetyIntegrationValidatorConfig,
  TMemorySafetyTestSuite
} from '../../../../../../shared/src/types/platform/integration/memory-safety-testing-types';

// ============================================================================
// SECTION 1: TEST SETUP AND CONFIGURATION
// ============================================================================

describe('MemorySafetyIntegrationValidator - Line Coverage Tests', () => {
  let validator: MemorySafetyIntegrationValidator;
  let mockConfig: TMemorySafetyIntegrationValidatorConfig;
  let mockTestSuite: TMemorySafetyTestSuite;

  beforeEach(async () => {
    validator = new MemorySafetyIntegrationValidator();
    
    mockConfig = {
      validatorId: 'line-test-validator',
      memorySafetyTestEnvironments: [{
        environmentId: 'test-env-1',
        environmentName: 'Test Environment',
        environmentType: 'memory-safety',
        systems: ['test-system'],
        memoryTools: ['test-tool'],
        isolation: true,
        monitoring: true,
        resourceLimits: {
          maxMemory: '1GB',
          maxCpu: '2',
          maxDuration: 300000,
          maxConcurrency: 5,
          maxStorage: '100MB',
          maxNetworkBandwidth: '100Mbps',
          metadata: {}
        },
        metadata: {}
      }],
      complianceStandards: [{
        standardId: 'mem-safe-002',
        standardName: 'MEM-SAFE-002',
        version: '1.0',
        applicablePatterns: ['memory-safe-inheritance'],
        validationFrequency: 'on-demand',
        complianceChecks: [{
          checkId: 'check-001',
          checkName: 'Memory Safe Inheritance Check',
          checkType: 'inheritance',
          validationCriteria: ['extends-base-tracking-service'],
          severity: 'high',
          metadata: {}
        }],
        metadata: {}
      }],
      memorySafetyTestSuites: [{
        suiteId: 'test-suite-1',
        suiteName: 'Test Suite',
        testCategories: ['leak-detection'],
        memorySafetyTests: [],
        executionSettings: {
          timeout: 300000,
          retryPolicy: {
            maxRetries: 1,
            retryDelay: 5000,
            backoffStrategy: 'linear',
            retryConditions: ['timeout'],
            metadata: {}
          },
          cleanupPolicy: 'always',
          parallelExecution: false,
          maxConcurrency: 1,
          metadata: {}
        },
        metadata: {}
      }],
      validationSettings: {
        enabledValidations: ['memory-leaks', 'compliance'],
        validationFrequency: 'manual',
        alertThresholds: [{
          thresholdId: 'memory-leak-threshold',
          metric: 'memory-growth',
          threshold: 10 * 1024 * 1024,
          timeWindow: 30000,
          severity: 'high',
          alertAction: 'notify',
          metadata: {}
        }],
        autoRemediation: false,
        reportingLevel: 'detailed',
        metadata: {}
      },
      monitoringSettings: {
        enabled: true,
        samplingInterval: 30000,
        retentionPeriod: 86400000,
        alerting: {
          enabled: true,
          alertChannels: ['email', 'dashboard'],
          escalationPolicy: 'standard',
          suppressionRules: [],
          metadata: {}
        },
        dataCollection: {
          enabled: true,
          collectionScope: ['memory', 'performance'],
          samplingRate: 1000,
          dataFormat: 'json',
          storageLocation: 'local',
          metadata: {}
        },
        metadata: {}
      },
      reportingSettings: {
        enabled: true,
        reportFormats: ['json'],
        deliveryMethods: ['file'],
        schedules: [{
          scheduleId: 'on-completion',
          frequency: 'daily',
          time: '00:00',
          timezone: 'UTC',
          enabled: true,
          metadata: {}
        }],
        recipients: ['<EMAIL>'],
        metadata: {}
      },
      securitySettings: {
        encryptionEnabled: true,
        auditingEnabled: true,
        accessControl: 'role-based',
        dataClassification: 'internal',
        complianceRequirements: ['MEM-SAFE-002'],
        metadata: {}
      },
      metadata: {}
    };

    mockTestSuite = {
      suiteId: 'line-test-suite',
      suiteName: 'Line Coverage Test Suite',
      testCategories: ['leak-detection'],
      memorySafetyTests: [{
        testId: 'line-test-001',
        testName: 'Line Coverage Test',
        testType: 'leak-detection',
        targetComponents: ['memory-validator'],
        testScenarios: [{
          scenarioId: 'scenario-001',
          description: 'Memory leak detection scenario',
          testSteps: ['initialize', 'allocate', 'monitor', 'cleanup'],
          memoryConstraints: {
            maxHeapSize: 100 * 1024 * 1024,
            maxStackSize: 10 * 1024 * 1024,
            maxObjectCount: 10000,
            maxAllocationRate: 1000,
            gcPressureLimit: 80,
            metadata: {}
          },
          expectedBehavior: 'No memory leaks detected',
          validationCriteria: ['memory-growth-within-threshold'],
          metadata: {}
        }],
        expectedResults: [{
          resultId: 'result-001',
          testId: 'line-test-001',
          expectedOutcome: 'pass',
          expectedMetrics: {
            maxMemoryUsage: 100 * 1024 * 1024,
            maxExecutionTime: 30000,
            expectedLeakCount: 0,
            expectedViolationCount: 0,
            performanceTargets: [],
            metadata: {}
          },
          validationCriteria: ['no-memory-leaks'],
          metadata: {}
        }],
        complianceRequirements: ['MEM-SAFE-002'],
        metadata: {}
      }],
      executionSettings: {
        timeout: 300000,
        retryPolicy: {
          maxRetries: 1,
          retryDelay: 5000,
          backoffStrategy: 'linear',
          retryConditions: ['timeout'],
          metadata: {}
        },
        cleanupPolicy: 'always',
        parallelExecution: false,
        maxConcurrency: 1,
        metadata: {}
      },
      metadata: {}
    };
  });

  afterEach(async () => {
    if (validator && validator.isReady()) {
      await validator.shutdown();
    }
  });

  // ============================================================================
  // SECTION 2: INITIALIZATION SEQUENCE LINE COVERAGE
  // ============================================================================

  describe('Initialization Sequence Line Coverage', () => {
    it('should execute every line in the initialization sequence', async () => {
      // Execute constructor lines
      expect(validator).toBeDefined();
      expect(validator.isReady()).toBe(false);

      // Execute base service initialization lines
      await validator.initialize();
      expect(validator.isReady()).toBe(true);

      // Execute memory safety validator initialization lines
      const initResult = await validator.initializeMemorySafetyValidator(mockConfig);
      expect(initResult.success).toBe(true);
      expect(initResult.validatorId).toBe('line-test-validator');
      expect(initResult.timestamp).toBeDefined();
      expect(initResult.metadata).toBeDefined();
    });

    it('should execute every line in the configuration validation sequence', async () => {
      await validator.initialize();

      // Test configuration validation with various edge cases to hit all lines
      const configs = [
        // Valid configuration
        mockConfig,
        
        // Configuration with minimal settings
        {
          ...mockConfig,
          validatorId: 'minimal-config',
          validationSettings: {
            ...mockConfig.validationSettings,
            validationScope: ['memory-leaks']
          }
        },
        
        // Configuration with all features enabled
        {
          ...mockConfig,
          validatorId: 'full-config',
          monitoringSettings: {
            ...mockConfig.monitoringSettings,
            enabled: true,
            monitoringScope: ['memory', 'performance', 'compliance']
          },
          reportingSettings: {
            ...mockConfig.reportingSettings,
            enabled: true,
            reportFormat: 'json',
            reportDestination: 'file'
          }
        }
      ];

      for (const config of configs) {
        await validator.shutdown();
        validator = new MemorySafetyIntegrationValidator();
        await validator.initialize();
        
        const result = await validator.initializeMemorySafetyValidator(config);
        expect(result.success).toBe(true);
        expect(result.validatorId).toBe(config.validatorId);
      }
    });

    it('should execute every line in the error handling initialization paths', async () => {
      await validator.initialize();

      // Test invalid configurations to hit error handling lines
      const invalidConfigs = [
        // Empty validator ID
        {
          ...mockConfig,
          validatorId: ''
        },
        
        // Invalid validation frequency
        {
          ...mockConfig,
          validationSettings: {
            ...mockConfig.validationSettings,
            validationFrequency: 'invalid-frequency' as any
          }
        },
        
        // Negative timeout
        {
          ...mockConfig,
          validationSettings: {
            ...mockConfig.validationSettings,
            validationTimeout: -1
          }
        },
        
        // Invalid thresholds
        {
          ...mockConfig,
          validationSettings: {
            ...mockConfig.validationSettings,
            validationThresholds: {
              memoryLeakThreshold: -1,
              complianceScore: 150, // Over 100%
              performanceThreshold: -1,
              metadata: {}
            }
          }
        }
      ];

      for (const invalidConfig of invalidConfigs) {
        const result = await validator.initializeMemorySafetyValidator(invalidConfig);
        expect(result.success).toBe(false);
        expect(result.metadata.error).toBeDefined();
      }
    });
  });

  // ============================================================================
  // SECTION 3: VALIDATION EXECUTION LINE COVERAGE
  // ============================================================================

  describe('Validation Execution Line Coverage', () => {
    beforeEach(async () => {
      await validator.initialize();
      await validator.initializeMemorySafetyValidator(mockConfig);
    });

    it('should execute every line in the validation start sequence', async () => {
      // Execute all lines in startMemorySafetyValidation
      const startResult = await validator.startMemorySafetyValidation();
      
      expect(startResult.success).toBe(true);
      expect(startResult.validationId).toBeDefined();
      expect(startResult.startTime).toBeDefined();
      expect(startResult.targetComponents).toBeDefined();
      expect(startResult.estimatedDuration).toBeDefined();
      expect(startResult.monitoringEnabled).toBeDefined();
      expect(startResult.metadata).toBeDefined();

      await validator.stopMemorySafetyValidation();
    });

    it('should execute every line in the validation stop sequence', async () => {
      // Start validation first
      await validator.startMemorySafetyValidation();
      
      // Execute all lines in stopMemorySafetyValidation
      const stopResult = await validator.stopMemorySafetyValidation();
      
      expect(stopResult.success).toBe(true);
      expect(stopResult.validationId).toBeDefined();
      expect(stopResult.stopTime).toBeDefined();
      expect(stopResult.metadata).toBeDefined();
    });

    it('should execute every line in the test execution sequence', async () => {
      // Execute all lines in executeMemorySafetyTest
      const testResult = await validator.executeMemorySafetyTest(mockTestSuite.memorySafetyTests[0]);
      
      expect(testResult.success).toBe(true);
      expect(testResult.testId).toBeDefined();
      expect(testResult.executionTime).toBeDefined();
      expect(testResult.memoryMetrics).toBeDefined();
      expect(testResult.metadata).toBeDefined();
    });

    it('should execute every line in the test suite validation sequence', async () => {
      // Execute all lines in validateMemorySafety
      const suiteResult = await validator.validateMemorySafety(mockTestSuite);
      
      expect(suiteResult.success).toBe(true);
      expect(suiteResult.testResults).toBeDefined();
      expect(suiteResult.metadata).toBeDefined();
    });
  });

  // ============================================================================
  // SECTION 4: ERROR HANDLING AND CLEANUP LINE COVERAGE
  // ============================================================================

  describe('Error Handling and Cleanup Line Coverage', () => {
    beforeEach(async () => {
      await validator.initialize();
      await validator.initializeMemorySafetyValidator(mockConfig);
    });

    it('should execute every line in error handling paths', async () => {
      // Force errors to execute error handling lines
      const originalMethod = (validator as any)._executeMemorySafetyTestInternal;
      
      // Mock to throw different types of errors
      const errors = [
        new Error('Standard error'),
        new TypeError('Type error'),
        new RangeError('Range error'),
        { message: 'Non-Error object' }, // Non-Error object
        'String error', // String error
        null, // Null error
        undefined // Undefined error
      ];

      for (const error of errors) {
        (validator as any)._executeMemorySafetyTestInternal = jest.fn().mockRejectedValue(error);
        
        const result = await validator.executeMemorySafetyTest(mockTestSuite.memorySafetyTests[0]);
        expect(result.success).toBe(false);
        expect(result.metadata.error).toBeDefined();
      }

      // Restore original method
      (validator as any)._executeMemorySafetyTestInternal = originalMethod;
    });

    it('should execute every line in cleanup sequences', async () => {
      // Start validation to create resources to clean up
      await validator.startMemorySafetyValidation();
      
      // Add some test history and active validations
      (validator as any)._testHistory.push({
        testId: 'cleanup-test',
        testName: 'Cleanup Test',
        status: 'passed',
        metadata: { executionTime: Date.now() }
      });

      // Execute cleanup lines through shutdown
      await validator.shutdown();
      
      // Verify cleanup was executed
      expect(validator.isReady()).toBe(false);
    });

    it('should execute every line in resource cleanup paths', async () => {
      // Create resources that need cleanup
      await validator.startMemorySafetyValidation();
      
      // Force cleanup through various paths
      const cleanupMethods = [
        '_stopAllActiveValidations',
        '_stopAllMonitoringSessions',
        '_cleanupValidationResources',
        '_resetValidatorState'
      ];

      for (const methodName of cleanupMethods) {
        const method = (validator as any)[methodName];
        if (method) {
          await method.call(validator, 'test-validation-id');
        }
      }

      await validator.stopMemorySafetyValidation();
    });
  });

  // ============================================================================
  // SECTION 5: COMPLEX LOGIC FLOW LINE COVERAGE
  // ============================================================================

  describe('Complex Logic Flow Line Coverage', () => {
    beforeEach(async () => {
      await validator.initialize();
      await validator.initializeMemorySafetyValidator(mockConfig);
    });

    it('should execute every line in test history management', async () => {
      // Add various types of test history entries
      const historyEntries = [
        {
          testId: 'history-test-1',
          testName: 'History Test 1',
          status: 'passed',
          metadata: { executionTime: Date.now() - 10000 }
        },
        {
          testId: 'history-test-2',
          testName: 'History Test 2',
          status: 'failed',
          metadata: { executionTime: Date.now() - 5000 }
        },
        {
          testId: 'history-test-3',
          testName: 'History Test 3',
          status: 'skipped',
          metadata: { executionTime: Date.now() - 1000 }
        }
      ];

      (validator as any)._testHistory = historyEntries;

      // Execute all lines in getMemorySafetyTestHistory
      const historyResult = await validator.getMemorySafetyTestHistory();
      expect(historyResult.testResults.length).toBe(0); // Initially empty
      expect(historyResult.totalTests).toBe(0);
      expect(historyResult.metadata).toBeDefined();

      // Execute all lines in clearMemorySafetyTestHistory with various criteria
      const clearCriteria = [
        {
          olderThan: new Date(Date.now() - 7000),
          testTypes: [],
          status: [],
          maxRecords: 100,
          metadata: {}
        },
        {
          olderThan: new Date(Date.now() - 20000),
          testTypes: ['History Test 1'],
          status: [],
          maxRecords: 100,
          metadata: {}
        },
        {
          olderThan: new Date(Date.now() - 20000),
          testTypes: [],
          status: ['failed'],
          maxRecords: 100,
          metadata: {}
        }
      ];

      for (const criteria of clearCriteria) {
        await validator.clearMemorySafetyTestHistory(criteria);
      }
    });

    it('should execute every line in metrics calculation', async () => {
      // Execute test to generate metrics
      await validator.executeMemorySafetyTest(mockTestSuite.memorySafetyTests[0]);
      
      // Execute all lines in getMemorySafetyMetrics
      const metricsResult = await validator.getMemorySafetyMetrics();

      expect(metricsResult.timestamp).toBeDefined();
      expect(metricsResult.metadata).toBeDefined();
    });

    it('should execute every line in report generation', async () => {
      // Execute test to have data for report
      await validator.executeMemorySafetyTest(mockTestSuite.memorySafetyTests[0]);
      
      // Execute all lines in generateMemorySafetyReport with different options
      const reportOptions = [
        undefined, // Default options
        {
          includeHistory: true,
          includeMetrics: true,
          includeDiagnostics: true,
          format: 'json' as const,
          metadata: {}
        },
        {
          includeHistory: false,
          includeMetrics: false,
          includeDiagnostics: false,
          format: 'text' as const,
          metadata: { customField: 'value' }
        }
      ];

      for (const options of reportOptions) {
        const reportResult = await validator.generateMemorySafetyReport(options);
        expect(reportResult.reportId).toBeDefined();
        expect(reportResult.generationTime).toBeDefined();
        expect(reportResult.reportData).toBeDefined();
        expect(reportResult.reportFormat).toBeDefined();
        expect(reportResult.reportSize).toBeDefined();
      }
    });

    it('should execute every line in health monitoring flows', async () => {
      // Execute all lines in getMemorySafetyTestHealth
      const healthResult = await validator.getMemorySafetyTestHealth();

      expect(healthResult.overallHealth).toBeDefined();
      expect(healthResult.activeIssues).toBeDefined();
      expect(healthResult.metadata).toBeDefined();

      // Execute all lines in getMemorySafetyStatus
      const statusResult = await validator.getMemorySafetyStatus();

      expect(statusResult.validatorStatus).toBeDefined();
      expect(statusResult.timestamp).toBeDefined();
      expect(statusResult.metadata).toBeDefined();
    });

    it('should execute every line in test type management flows', async () => {
      // Execute all lines in enableMemorySafetyTestType
      const testTypes = ['performance', 'compliance-check', 'resource-usage'];

      for (const testType of testTypes) {
        await expect(validator.enableMemorySafetyTestType(testType as any)).resolves.not.toThrow();
      }

      // Execute all lines in disableMemorySafetyTestType
      for (const testType of testTypes) {
        await expect(validator.disableMemorySafetyTestType(testType as any)).resolves.not.toThrow();
      }

      // Test additional type management operations
      await expect(validator.enableMemorySafetyTestType('leak-detection')).resolves.not.toThrow();
      await expect(validator.disableMemorySafetyTestType('leak-detection')).resolves.not.toThrow();
    });

    it('should execute every line in edge case handling flows', async () => {
      // Test with extreme memory conditions
      const originalMemoryUsage = process.memoryUsage;

      // Simulate high memory usage
      process.memoryUsage = (() => ({
        rss: 4 * 1024 * 1024 * 1024, // 4GB
        heapTotal: 3 * 1024 * 1024 * 1024, // 3GB
        heapUsed: 2.8 * 1024 * 1024 * 1024, // 2.8GB
        external: 200 * 1024 * 1024, // 200MB
        arrayBuffers: 100 * 1024 * 1024 // 100MB
      })) as any;

      // Execute operations under high memory conditions
      const highMemoryTest = await validator.executeMemorySafetyTest(mockTestSuite.memorySafetyTests[0]);
      expect(highMemoryTest.success).toBeDefined();

      // Simulate low memory conditions
      process.memoryUsage = (() => ({
        rss: 50 * 1024 * 1024, // 50MB
        heapTotal: 40 * 1024 * 1024, // 40MB
        heapUsed: 30 * 1024 * 1024, // 30MB
        external: 5 * 1024 * 1024, // 5MB
        arrayBuffers: 2 * 1024 * 1024 // 2MB
      })) as any;

      // Execute operations under low memory conditions
      const lowMemoryTest = await validator.executeMemorySafetyTest(mockTestSuite.memorySafetyTests[0]);
      expect(lowMemoryTest.success).toBeDefined();

      // Restore original function
      process.memoryUsage = originalMemoryUsage;
    });

    it('should execute every line in concurrent operation handling', async () => {
      // Test concurrent validation attempts
      const concurrentPromises: Promise<any>[] = [];

      for (let i = 0; i < 5; i++) {
        concurrentPromises.push(validator.startMemorySafetyValidation());
      }

      const concurrentResults = await Promise.all(concurrentPromises);

      // Only first should succeed, others should fail gracefully
      const successCount = concurrentResults.filter(result => result.success).length;
      expect(successCount).toBe(1);

      // Clean up
      await validator.stopMemorySafetyValidation();

      // Test concurrent test executions
      const concurrentTestPromises: Promise<any>[] = [];

      for (let i = 0; i < 3; i++) {
        concurrentTestPromises.push(
          validator.executeMemorySafetyTest(mockTestSuite.memorySafetyTests[0])
        );
      }

      const concurrentTestResults = await Promise.all(concurrentTestPromises);

      // All should complete successfully
      expect(concurrentTestResults.every(result => result.success)).toBe(true);
    });

    it('should execute every line in shutdown and cleanup sequences', async () => {
      // Create complex state to clean up
      await validator.startMemorySafetyValidation();
      await validator.executeMemorySafetyTest(mockTestSuite.memorySafetyTests[0]);

      // Add multiple test history entries
      for (let i = 0; i < 10; i++) {
        (validator as any)._testHistory.push({
          testId: `cleanup-test-${i}`,
          testName: `Cleanup Test ${i}`,
          status: i % 2 === 0 ? 'passed' : 'failed',
          metadata: { executionTime: Date.now() - (i * 1000) }
        });
      }

      // Add multiple active validations
      for (let i = 0; i < 5; i++) {
        (validator as any)._activeValidations.set(`validation-${i}`, {
          validationId: `validation-${i}`,
          status: 'running',
          startTime: Date.now() - (i * 1000)
        });
      }

      // Execute shutdown to trigger all cleanup lines
      await validator.shutdown();

      // Verify complete cleanup
      expect(validator.isReady()).toBe(false);
      expect((validator as any)._activeValidations.size).toBe(0);
    });

    it('should execute every line in diagnostic and monitoring flows', async () => {
      // Execute all diagnostic methods to hit every line
      const diagnosticMethods = [
        '_performSystemDiagnostics',
        '_performValidatorDiagnostics',
        '_performPerformanceDiagnostics'
      ];

      for (const methodName of diagnosticMethods) {
        const method = (validator as any)[methodName];
        if (method) {
          const result = await method.call(validator);
          expect(result).toBeDefined();
        }
      }

      // Execute monitoring methods
      const monitoringMethods = [
        '_monitorMemorySafetyOperations',
        '_checkSystemHealth'
      ];

      for (const methodName of monitoringMethods) {
        const method = (validator as any)[methodName];
        if (method) {
          const result = await method.call(validator);
          expect(result).toBeDefined();
        }
      }

      // Execute integration processing methods
      const integrationMethods = [
        '_processMemorySafetyIntegrationData',
        '_updateValidatorStateFromIntegration'
      ];

      for (const methodName of integrationMethods) {
        const method = (validator as any)[methodName];
        if (method) {
          if (methodName === '_processMemorySafetyIntegrationData') {
            const result = await method.call(validator, { test: 'data' });
            expect(result).toBeDefined();
          } else {
            await method.call(validator, { processedId: 'test-id' });
          }
        }
      }
    });
  });

  // ============================================================================
  // SECTION 6: EXCEPTION PATH LINE COVERAGE
  // ============================================================================

  describe('Exception Path Line Coverage', () => {
    beforeEach(async () => {
      await validator.initialize();
      await validator.initializeMemorySafetyValidator(mockConfig);
    });

    it('should execute every line in exception handling paths', async () => {
      // Test various exception scenarios to hit all exception handling lines
      const exceptionScenarios = [
        {
          name: 'JSON parsing error',
          setup: () => {
            // Create circular reference to cause JSON.stringify error
            const circularObj = { test: 'data' };
            (circularObj as any).circular = circularObj;
            return circularObj;
          }
        },
        {
          name: 'Memory allocation error',
          setup: () => {
            // Simulate memory allocation failure
            const originalArrayBuffer = global.ArrayBuffer;
            global.ArrayBuffer = function() {
              throw new Error('Memory allocation failed');
            } as any;
            return () => { global.ArrayBuffer = originalArrayBuffer; };
          }
        },
        {
          name: 'Timer creation error',
          setup: () => {
            // Mock timer functions to throw errors
            const originalSetTimeout = global.setTimeout;
            global.setTimeout = function() {
              throw new Error('Timer creation failed');
            } as any;
            return () => { global.setTimeout = originalSetTimeout; };
          }
        }
      ];

      for (const scenario of exceptionScenarios) {
        try {
          const cleanup = scenario.setup();

          // Execute operations that should trigger exception handling
          await validator.executeMemorySafetyTest(mockTestSuite.memorySafetyTests[0]);

          if (typeof cleanup === 'function') {
            cleanup();
          }
        } catch (error) {
          // Expected - exception handling lines should be executed
          expect(error).toBeDefined();
        }
      }
    });

    it('should execute every line in recovery and fallback paths', async () => {
      // Test recovery mechanisms
      const recoveryMethods = [
        '_recoverFromValidationError',
        '_handleValidationError',
        '_resetValidatorState'
      ];

      for (const methodName of recoveryMethods) {
        const method = (validator as any)[methodName];
        if (method) {
          try {
            if (methodName === '_handleValidationError') {
              await method.call(validator, new Error('Test error'), 'test-context');
            } else if (methodName === '_recoverFromValidationError') {
              await method.call(validator, 'test-validation-id');
            } else {
              await method.call(validator);
            }
          } catch (error) {
            // Some recovery methods may throw - this is expected
            expect(error).toBeDefined();
          }
        }
      }
    });
  });
});
